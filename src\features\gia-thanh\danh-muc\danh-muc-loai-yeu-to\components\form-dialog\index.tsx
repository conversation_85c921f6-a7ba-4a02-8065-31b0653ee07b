'use client';

import { useState, useEffect } from 'react';
import { But<PERSON> } from '@mui/material';
import { AritoDialog } from '@/components/custom/arito/dialog';
import { FormSchema, initialFormValues } from '../../schema';
import { AritoForm } from '@/components/custom/arito/form';
import AritoIcon from '@/components/custom/arito/icon';
import { ConfirmDialog } from '../../components';
import { BasicInfoTab } from './BasicInfoTab';
import { LoaiYeuTo } from '@/types/schemas';
import { FormMode } from '@/types/form';

interface FormDialogProps {
  open: boolean;
  onClose: () => void;
  onSubmit: (data: any) => void;
  formMode: FormMode;
  initialData?: any;
  loaiYeuTos: LoaiYeuTo[];
  onAdd?: () => void;
  onEdit?: () => void;
  onDelete?: () => void;
  onCopy?: () => void;
}

const FormDialog = ({
  open,
  onClose,
  onSubmit,
  formMode,
  initialData,
  loaiYeuTos,
  onAdd,
  onEdit,
  onDelete,
  onCopy
}: FormDialogProps) => {
  const [isConfirm, setIsConfirm] = useState<boolean>(false);
  const [isFormDirty, setIsFormDirty] = useState<boolean>(false);
  const [showDuplicateCodeDialog, setShowDuplicateCodeDialog] = useState(false);

  useEffect(() => {
    if (!open) {
      setIsFormDirty(false);
    }
  }, [open]);

  const handleSubmit = (data: any) => {
    try {
      if (formMode === 'add') {
        const isDuplicate = loaiYeuTos.some(item => item.ma_loai === data.ma_loai);
        if (isDuplicate) {
          setShowDuplicateCodeDialog(true);
          return;
        }
      } else if (formMode === 'edit' && initialData) {
        const isDuplicate = loaiYeuTos.some(item => item.ma_loai === data.ma_loai && item.uuid !== initialData.uuid);
        if (isDuplicate) {
          setShowDuplicateCodeDialog(true);
          return;
        }
      }

      onSubmit?.(data);
      setIsFormDirty(false);
      onClose();
    } catch (err) {
      console.error('❌ FormDialog: Error in handleSubmit', err);
    }
  };

  const handleClose = () => {
    if (isFormDirty && formMode !== 'view') {
      setIsConfirm(true);
    } else {
      onClose();
    }
  };

  const viewActions = [
    { onClick: onAdd, icon: 571, text: 'Thêm' },
    { onClick: onEdit, icon: 9, text: 'Sửa' },
    { onClick: onDelete, icon: 8, text: 'Xoá' },
    { onClick: onCopy, icon: 11, text: 'Sao chép' },
    { onClick: onClose, icon: 885, text: 'Đóng' }
  ];

  const getDialogTitle = () => {
    switch (formMode) {
      case 'add':
        return 'Mới';
      case 'edit':
        return 'Sửa';
      case 'view':
        return 'Xem';
      default:
        return 'Loại yếu tố';
    }
  };

  return (
    <>
      <AritoDialog
        open={open}
        onClose={handleClose}
        title={getDialogTitle()}
        maxWidth='xl'
        disableBackdropClose={false}
        disableEscapeKeyDown={true}
        titleIcon={<AritoIcon icon={281} />}
      >
        <AritoForm
          mode={formMode}
          hasAritoActionBar={false}
          schema={FormSchema}
          onSubmit={handleSubmit}
          initialData={
            formMode === 'add' && initialData
              ? {
                  ...(initialData as any),
                  uuid: undefined,
                  ma_loai: ''
                }
              : (initialData as any) || initialFormValues
          }
          className='w-[900px]'
          headerFields={
            <div className='max-h-[calc(100vh-150px)] overflow-y-auto' onChange={() => setIsFormDirty(true)}>
              <BasicInfoTab formMode={formMode} />
            </div>
          }
          classNameBottomBar='relative w-full flex justify-end gap-2 p-1'
          bottomBar={
            <>
              {formMode === 'view' && (
                <>
                  {viewActions.map(({ onClick, icon, text }) => (
                    <Button key={text} onClick={() => onClick?.()} variant='outlined'>
                      <AritoIcon icon={icon} className='mr-2' />
                      {text}
                    </Button>
                  ))}
                </>
              )}
              {formMode !== 'view' && (
                <>
                  <Button
                    type='submit'
                    variant='contained'
                    className='bg-[rgba(15,118,110,0.9)] normal-case hover:bg-[rgba(15,118,110,1)]'
                  >
                    <AritoIcon icon={884} className='mr-2' />
                    Đồng ý
                  </Button>
                  <Button onClick={handleClose} variant='outlined'>
                    <AritoIcon icon={885} className='mr-2' />
                    Huỷ
                  </Button>
                </>
              )}
            </>
          }
        />
      </AritoDialog>

      <AritoDialog
        open={showDuplicateCodeDialog}
        onClose={() => setShowDuplicateCodeDialog(false)}
        title='Cảnh báo'
        maxWidth='sm'
        disableBackdropClose={false}
        disableEscapeKeyDown={false}
        titleIcon={<AritoIcon icon={260} />}
      >
        <div className='p-4'>
          <p className='mb-4 text-center'>Mã loại yếu tố đã tồn tại trong danh mục</p>
          <div className='flex justify-end'>
            <Button
              className='bg-[rgba(15,118,110,0.9)] text-white hover:bg-[rgba(15,118,110,1)]'
              onClick={() => setShowDuplicateCodeDialog(false)}
              variant='contained'
            >
              <AritoIcon icon={884} className='mr-2' />
              Đồng ý
            </Button>
          </div>
        </div>
      </AritoDialog>

      {isConfirm && (
        <ConfirmDialog
          open={isConfirm}
          onClose={() => setIsConfirm(false)}
          onConfirm={() => {
            setIsConfirm(false);
            setIsFormDirty(false);
            onClose();
          }}
          title='Cảnh báo'
          content='Bạn muốn kết thúc?'
        />
      )}
    </>
  );
};

export default FormDialog;
