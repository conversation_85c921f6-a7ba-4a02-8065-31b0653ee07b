/**
 * TypeScript interface for ChungTu (Document/Voucher) model
 *
 * This interface represents the structure of the ChungTu model from the backend.
 * It defines documents and vouchers used in the accounting system.
 */

import { ApiResponse } from '@/types/api.type';

/**
 * Enum for document status types
 */
export enum ChungTuStatusType {
  LAP_CHUNG_TU = 'lap_chung_tu',
  CHO_DUYET = 'cho_duyet',
  DANG_DUYET = 'dang_duyet',
  DA_DUYET = 'da_duyet',
  DANG_THUC_HIEN = 'dang_thuc_hien',
  HOAN_THANH = 'hoan_thanh',
  DONG = 'dong'
}

/**
 * Enum for document types
 */
export enum ChungTuType {
  ALL = 'all',
  TIEN_MAT = 'tien_mat',
  TIEN_GUI = 'tien_gui',
  BAN_HANG = 'ban_hang',
  HOA_DON = 'hoa_don',
  MUA_HANG = 'mua_hang',
  TON_KHO = 'ton_kho',
  GIA_THANH = 'gia_thanh',
  THUE = 'thue',
  TONG_HOP = 'tong_hop',
  E_PROCUREMENT = 'e_procurement',
  KHO = 'kho',
  NHAN_SU = 'nhan_su',
  HOA_DON_DAU_VAO = 'hoa_don_dau_vao'
}

export interface ChungTu {
  /**
   * Unique identifier (UUID)
   */
  uuid: string;

  /**
   * Reference to the entity model
   */
  entity_model?: string;

  /**
   * Document code
   */
  ma_ct: string;

  /**
   * Document name
   */
  ten_ct?: string | null;

  /**
   * Alternative document name
   */
  ten_ct2?: string | null;

  /**
   * Closing date
   */
  ngay_ks: string;

  /**
   * Sequence number
   */
  stt: string;

  /**
   * Current document number
   */
  so_chung_tu_hien_tai: string;

  /**
   * Maximum number of rows
   */
  so_dong_toi_da: number;

  /**
   * Default status
   */
  trang_thai_ngam_dinh?: ChungTuStatusType | null;

  /**
   * Show creator information
   */
  hien_thi_thong_tin_nguoi_tao: boolean;

  /**
   * Show last editor information
   */
  hien_thi_thong_tin_nguoi_sua_gan_nhat: boolean;

  /**
   * Show document creation date field
   */
  hien_thi_truong_ngay_lap_chung_tu: boolean;

  /**
   * Transportation link
   */
  vc_link?: string | null;

  /**
   * Related document code
   */
  ct_lien_quan?: string | null;

  /**
   * Save log
   */
  ct_save_log?: string | null;

  /**
   * Table data (JSON)
   */
  table?: any | null;

  /**
   * Document type
   */
  loai_chung_tu?: ChungTuType | null;

  /**
   * Timestamp of creation
   */
  created?: string;

  /**
   * Timestamp of last update
   */
  updated?: string;

  /**
   * Document number (i_so_ct in backend)
   */
  i_so_ct?: number;

  /**
   * Page count
   */
  d_page_count?: number;

  /**
   * Order type
   */
  order_type?: string;

  /**
   * Status (df_status in backend)
   */
  df_status?: string;
}

/**
 * Type for ChungTu API response
 */
export type ChungTuResponse = ApiResponse<ChungTu>;

/**
 * Simplified ChungTu type for use in forms and components
 */
export interface ChungTuSimple {
  /**
   * Document code
   */
  ma_ct: string;

  /**
   * Document name
   */
  ten_ct: string;
}

/**
 * Type for creating or updating a ChungTu
 */
export interface ChungTuInput {
  /**
   * Document code
   */
  ma_ct: string;

  /**
   * Document name
   */
  ten_ct?: string | null;

  /**
   * Alternative document name
   */
  ten_ct2?: string | null;

  /**
   * Closing date
   */
  ngay_ks: string;

  /**
   * Sequence number
   */
  stt: string;

  /**
   * Current document number
   */
  so_chung_tu_hien_tai: string;

  /**
   * Maximum number of rows
   */
  so_dong_toi_da: number;

  /**
   * Default status
   */
  trang_thai_ngam_dinh?: ChungTuStatusType | null;

  /**
   * Show creator information
   */
  hien_thi_thong_tin_nguoi_tao?: boolean;

  /**
   * Show last editor information
   */
  hien_thi_thong_tin_nguoi_sua_gan_nhat?: boolean;

  /**
   * Show document creation date field
   */
  hien_thi_truong_ngay_lap_chung_tu?: boolean;

  /**
   * Transportation link
   */
  vc_link?: string | null;

  /**
   * Related document code
   */
  ct_lien_quan?: string | null;

  /**
   * Save log
   */
  ct_save_log?: string | null;

  /**
   * Table data (JSON)
   */
  table?: any | null;

  /**
   * Document type
   */
  loai_chung_tu?: ChungTuType | null;

  /**
   * Document number (i_so_ct in backend)
   */
  i_so_ct?: number;

  /**
   * Page count
   */
  d_page_count?: number;

  /**
   * Order type
   */
  order_type?: string;

  /**
   * Status (df_status in backend)
   */
  df_status?: string;
}

/**
 * Type for ChungTu search parameters
 */
export interface ChungTuSearchParams {
  /**
   * Document code
   */
  ma_ct?: string;

  /**
   * Document name
   */
  ten_ct?: string;

  /**
   * Document type
   */
  loai_chung_tu?: ChungTuType;

  /**
   * Status
   */
  trang_thai_ngam_dinh?: ChungTuStatusType;

  /**
   * Entity slug
   */
  entity_slug?: string;

  /**
   * Page number for pagination
   */
  page?: number;

  /**
   * Page size for pagination
   */
  page_size?: number;
}

/**
 * Type for ChungTu list response
 */
export type ChungTuListResponse = ApiResponse<ChungTu>;

/**
 * Type for ChungTu with related data
 */
export interface ChungTuWithRelatedData extends ChungTu {
  /**
   * Entity data
   */
  entity_model_data?: any;

  /**
   * Related document data
   */
  ct_lien_quan_data?: ChungTuSimple | null;
}
