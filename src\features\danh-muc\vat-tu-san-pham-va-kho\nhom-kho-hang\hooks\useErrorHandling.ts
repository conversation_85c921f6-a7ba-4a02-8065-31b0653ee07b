import { useState, useRef, useEffect } from 'react';

interface ErrorDetails {
  message: string;
  code?: string;
  operation: string;
  timestamp: string;
  retryCount: number;
  maxRetries: number;
}

export const useErrorHandling = () => {
  const [errors, setErrors] = useState<ErrorDetails[]>([]);
  const [isRetrying, setIsRetrying] = useState<boolean>(false);
  const errorsRef = useRef<ErrorDetails[]>([]);

  useEffect(() => {
    errorsRef.current = errors;
  }, [errors]);

  const handleError = (error: any, operation: string, showNotification: boolean = true): ErrorDetails => {
    let message = 'Đ<PERSON> xảy ra lỗi không xác định';
    let code = 'UNKNOWN_ERROR';

    if (error instanceof Error) {
      message = error.message;
    } else if (typeof error === 'string') {
      message = error;
    } else if (error?.response?.data?.message) {
      message = error.response.data.message;
      code = error.response.status.toString();
    } else if (error?.message) {
      message = error.message;
    }

    const errorDetails: ErrorDetails = {
      message,
      code,
      operation,
      timestamp: new Date().toISOString(),
      retryCount: 0,
      maxRetries: 3
    };

    setErrors(prev => [...prev, errorDetails]);

    if (showNotification) {
      console.error(`${operation}: ${message}`);
    }

    console.error(`Error in ${operation}:`, error);

    return errorDetails;
  };

  const retryWithBackoff = async <T>(
    operation: () => Promise<T>,
    operationName: string,
    maxRetries: number = 3
  ): Promise<T> => {
    setIsRetrying(true);

    let retryCount = 0;

    const executeWithRetry = async (): Promise<T> => {
      try {
        return await operation();
      } catch (error) {
        if (retryCount >= maxRetries) {
          handleError(error, operationName);
          throw error;
        }

        retryCount++;

        const backoffTime = Math.min(1000 * Math.pow(2, retryCount) + Math.random() * 1000, 10000);

        await new Promise(resolve => setTimeout(resolve, backoffTime));

        return executeWithRetry();
      }
    };

    try {
      return await executeWithRetry();
    } finally {
      setIsRetrying(false);
    }
  };

  const clearErrors = () => {
    setErrors([]);
  };

  const clearError = (index: number) => {
    setErrors(prev => prev.filter((_, i) => i !== index));
  };

  return {
    errors,
    isRetrying,
    handleError,
    retryWithBackoff,
    clearErrors,
    clearError
  };
};
