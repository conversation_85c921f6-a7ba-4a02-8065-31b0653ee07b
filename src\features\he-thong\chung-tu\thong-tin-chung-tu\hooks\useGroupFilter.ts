import { useState } from 'react';

interface GroupType {
  label: string;
  value: string;
}

const Group: GroupType[] = [
  { label: 'Tiền mặt', value: 'tien_mat' },
  { label: 'Tiền gửi', value: 'tien_gui' },
  { label: '<PERSON><PERSON> hàng', value: 'ban_hang' },
  { label: 'Hóa đơn', value: 'hoa_don' },
  { label: 'Mua hàng', value: 'mua_hang' },
  { label: 'Tồn kho', value: 'ton_kho' },
  { label: 'Giá thành', value: 'gia_thanh' },
  { label: 'Thuế', value: 'thue' },
  { label: 'Tổng hợp', value: 'tong_hop' },
  { label: 'e-Procurement', value: 'e_procurement' },
  { label: 'Kho', value: 'kho' },
  { label: 'Nhân sự', value: 'nhan_su' },
  { label: 'Hóa đơn đầu vào', value: 'hoa_don_dau_vao' }
];

interface UseGroupFilterReturn {
  activeGroup: string;
  sidebarOpen: boolean;
  Group: GroupType[];

  handleFilter: (groupValue: string) => void;
  toggleSidebar: () => void;
}

const useGroupFilter = (): UseGroupFilterReturn => {
  const [activeGroup, setActiveGroup] = useState<string>(Group[0].value);
  const [sidebarOpen, setSidebarOpen] = useState(true);

  const handleFilter = (groupValue: string) => {
    setActiveGroup(groupValue);
  };

  const toggleSidebar = () => {
    setSidebarOpen(!sidebarOpen);
  };

  return {
    Group,
    activeGroup,
    sidebarOpen,

    handleFilter,
    toggleSidebar
  };
};

export default useGroupFilter;
