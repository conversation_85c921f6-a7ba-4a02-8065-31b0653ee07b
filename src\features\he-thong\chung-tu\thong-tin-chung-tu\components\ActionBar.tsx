import { <PERSON><PERSON><PERSON>, Pin, RefreshCcw, List } from 'lucide-react';
import { AritoActionButton } from '@/components/custom/arito/action-button';
import AritoActionBar from '@/components/custom/arito/action-bar';

/**
 * Props for the ActionBar component
 */
interface ActionBarProps {
  isViewDisabled?: boolean;
  onEditClick?: () => void;
  onRefreshClick?: () => void;
  onFixedColumnsClick?: () => void;
  onAddFieldClick?: () => void;
}

/**
 * ActionBar component for the Balance Sheet by Unit page
 */
const ActionBar: React.FC<ActionBarProps> = ({
  isViewDisabled = false,
  onEditClick,
  onRefreshClick,
  onFixedColumnsClick,
  onAddFieldClick
}) => (
  <AritoActionBar
    titleComponent={
      <div>
        <h1 className='text-xl font-bold'><PERSON>h mục chứng từ</h1>
        <span className='flex items-center justify-start text-xs font-semibold text-gray-500'>
          <div className='mr-2 size-2 rounded-full bg-red-500' />
          S<PERSON>a kho<PERSON> sổ chứng từ
        </span>
      </div>
    }
  >
    {onEditClick && <AritoActionButton title='Sửa' icon={Pencil} onClick={onEditClick} disabled={true} />}
    {onRefreshClick && (
      <AritoActionButton title='Refresh' icon={RefreshCcw} onClick={onRefreshClick} disabled={isViewDisabled} />
    )}
    {onFixedColumnsClick && (
      <AritoActionButton title='Cố định cột' icon={Pin} onClick={onFixedColumnsClick} disabled={isViewDisabled} />
    )}
    {onAddFieldClick && <AritoActionButton title='Thêm trường' icon={List} onClick={onAddFieldClick} disabled={true} />}
  </AritoActionBar>
);

export default ActionBar;
