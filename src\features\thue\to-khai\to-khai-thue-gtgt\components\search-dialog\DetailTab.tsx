import React, { useState } from 'react';
import RadixHoverDropdown from '@/components/custom/arito/hover-dropdown/radix-hover-dropdown';
import { RadioButton } from '@/components/custom/arito/form/radio-button';
import { FormField } from '@/components/custom/arito/form/form-field';
import { Label } from '@/components/ui/label';

interface DetailTabProps {
  formMode: 'add' | 'edit' | 'view';
}

const DetailTab: React.FC<DetailTabProps> = ({ formMode }) => {
  return (
    <div className='space-y-6 p-4'>
      <div className='space-y-4'>
        {/* Loại tờ khai */}
        <div className='flex items-center'>
          <Label className='w-32 min-w-32 text-sm font-medium'>Loại tờ khai</Label>
          <div className='flex space-x-4'>
            <RadioButton
              name='loai_to_khai'
              options={[
                { value: 'to_khai_lan_dau', label: 'Tờ khai lần đầu' },
                { value: 'to_khai_bo_sung', label: 'Tờ khai bổ sung' }
              ]}
            />
          </div>
        </div>

        {/* Chọn báo cáo */}
        <div className='flex items-center'>
          <Label className='w-32 min-w-32 text-sm font-medium'>Chọn báo cáo</Label>
          <FormField
            name='chon_bao_cao'
            type='select'
            className='mr-2 w-96'
            options={[
              { value: 'mau_thong_tu_80_2021', label: 'Mẫu thông tư 80/2021' },
              { value: 'mau_thong_tu_156_2013', label: 'Mẫu thông tư 156/2013' }
            ]}
            disabled={formMode === 'view'}
          />
          <div className='flex h-9 w-9 flex-shrink-0 items-center justify-center'>
            <RadixHoverDropdown
              iconNumber={'...'}
              items={[
                {
                  value: 'save_new',
                  label: 'Tạo mẫu báo cáo mới',
                  icon: 7,
                  onClick: () => console.log('Save new filter template')
                },
                {
                  value: 'save_overwrite',
                  label: 'Sửa mẫu đang chọn',
                  icon: 9,
                  onClick: () => console.log('Overwrite current filter template')
                },
                {
                  value: 'delete',
                  label: 'Xóa mẫu đang chọn',
                  icon: 8,
                  onClick: () => console.log('Delete current filter template')
                }
              ]}
            />
          </div>
        </div>

        {/* Mẫu báo cáo */}
        <div className='flex items-center'>
          <Label className='w-32 min-w-32 text-sm font-medium'>Mẫu báo cáo</Label>
          <FormField
            name='mau_bao_cao'
            type='select'
            className='mr-2 w-96'
            options={[
              { value: 'mau_tien_chuan', label: 'Mẫu tiền chuẩn' },
              { value: 'mau_ngoai_te', label: 'Mẫu ngoại tệ' }
            ]}
            disabled={formMode === 'view'}
          />
        </div>
      </div>
    </div>
  );
};

export default DetailTab;
