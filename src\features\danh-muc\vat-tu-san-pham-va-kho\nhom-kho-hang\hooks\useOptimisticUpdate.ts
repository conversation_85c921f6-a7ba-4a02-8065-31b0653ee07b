import { useState, useRef, useEffect } from 'react';
import { Group, GroupType } from '@/types/schemas';

export const useOptimisticUpdate = (initialGroups: Group[] = []) => {
  const [groups, setGroups] = useState<Group[]>(initialGroups);
  const [pendingOperations, setPendingOperations] = useState<Map<string, { type: string; data: any }>>(new Map());

  const groupsRef = useRef<Group[]>(initialGroups);

  useEffect(() => {
    groupsRef.current = groups;
  }, [groups]);

  const updateGroups = (newGroups: Group[]) => {
    setGroups(newGroups);
  };

  const optimisticAddGroup = async (
    newGroup: any,
    activeGroup: GroupType,
    addGroupFn: (data: any) => Promise<Group | undefined | any>
  ) => {
    const tempUuid = `temp-${Date.now()}`;

    const tempGroup: Group = {
      uuid: tempUuid,
      entity_model: 'group',
      ma_nhom: newGroup.ma_nhom,
      ten_phan_nhom: newGroup.ten_phan_nhom,
      ten2: newGroup.ten2 || null,
      trang_thai: newGroup.trang_thai,
      loai_nhom: activeGroup,
      created: new Date().toISOString(),
      updated: new Date().toISOString()
    };

    setGroups(prev => [tempGroup, ...prev]);

    setPendingOperations(prev => {
      const newMap = new Map(prev);
      newMap.set(tempUuid, { type: 'add', data: newGroup });
      return newMap;
    });

    try {
      const result = await addGroupFn({
        ...newGroup,
        loai_nhom: activeGroup
      });

      setGroups(prev => prev.map(group => (group.uuid === tempUuid ? result : group)));

      setPendingOperations(prev => {
        const newMap = new Map(prev);
        newMap.delete(tempUuid);
        return newMap;
      });

      return result;
    } catch (error) {
      setGroups(prev => prev.filter(group => group.uuid !== tempUuid));

      setPendingOperations(prev => {
        const newMap = new Map(prev);
        newMap.delete(tempUuid);
        return newMap;
      });

      throw error;
    }
  };

  const optimisticUpdateGroup = async (
    uuid: string,
    updatedGroup: any,
    activeGroup: GroupType,
    updateGroupFn: (uuid: string, data: any) => Promise<Group | undefined | any>
  ) => {
    const originalGroup = groupsRef.current.find(group => group.uuid === uuid);

    if (!originalGroup) {
      throw new Error(`Group with UUID ${uuid} not found`);
    }

    setGroups(prev =>
      prev.map(group =>
        group.uuid === uuid
          ? {
              ...group,
              ma_nhom: updatedGroup.ma_nhom,
              ten_phan_nhom: updatedGroup.ten_phan_nhom,
              ten2: updatedGroup.ten2 || null,
              trang_thai: updatedGroup.trang_thai,
              updated: new Date().toISOString()
            }
          : group
      )
    );

    setPendingOperations(prev => {
      const newMap = new Map(prev);
      newMap.set(uuid, { type: 'update', data: originalGroup });
      return newMap;
    });

    try {
      const result = await updateGroupFn(uuid, {
        ...updatedGroup,
        loai_nhom: activeGroup
      });

      setGroups(prev => prev.map(group => (group.uuid === uuid ? result : group)));

      setPendingOperations(prev => {
        const newMap = new Map(prev);
        newMap.delete(uuid);
        return newMap;
      });

      return result;
    } catch (error) {
      setGroups(prev => prev.map(group => (group.uuid === uuid ? originalGroup : group)));

      setPendingOperations(prev => {
        const newMap = new Map(prev);
        newMap.delete(uuid);
        return newMap;
      });

      throw error;
    }
  };

  const optimisticDeleteGroup = async (
    uuid: string,
    deleteGroupFn: (uuid: string) => Promise<boolean | undefined | any>
  ) => {
    const originalGroup = groupsRef.current.find(group => group.uuid === uuid);

    if (!originalGroup) {
      throw new Error(`Group with UUID ${uuid} not found`);
    }

    setGroups(prev => prev.filter(group => group.uuid !== uuid));

    setPendingOperations(prev => {
      const newMap = new Map(prev);
      newMap.set(uuid, { type: 'delete', data: originalGroup });
      return newMap;
    });

    try {
      const result = await deleteGroupFn(uuid);

      setPendingOperations(prev => {
        const newMap = new Map(prev);
        newMap.delete(uuid);
        return newMap;
      });

      return result;
    } catch (error) {
      setGroups(prev => [...prev, originalGroup]);

      setPendingOperations(prev => {
        const newMap = new Map(prev);
        newMap.delete(uuid);
        return newMap;
      });

      throw error;
    }
  };

  return {
    groups,
    pendingOperations,
    updateGroups,
    optimisticAddGroup,
    optimisticUpdateGroup,
    optimisticDeleteGroup
  };
};
