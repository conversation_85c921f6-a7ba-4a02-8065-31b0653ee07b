import { DoiTuong } from './doi-tuong.type';

/**
 * Interface for NguonDon (Order Source) model
 */
export interface NguonDon extends DoiTuong {
  /**
   * Order source code
   */
  ma_nguondon: string;

  /**
   * Order source name
   */
  ten_nguondon: string;

  /**
   * Additional notes
   */
  ghi_chu: string | null;

  /**
   * Status (1 = active, 0 = inactive)
   */
  status: number;
}

/**
 * Interface for NguonDon API response
 */
export interface NguonDonResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: NguonDon[];
}

/**
 * Interface for creating or updating a NguonDon
 */
export interface NguonDonInput {
  ma_nguondon: string;
  ten_nguondon: string;
  ghi_chu?: string | null;
  status: string | number;
}
