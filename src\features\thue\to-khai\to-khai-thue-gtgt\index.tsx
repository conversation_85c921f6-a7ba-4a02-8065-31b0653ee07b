'use client';

import React, { useState } from 'react';
import AritoDataTables from '@/components/custom/arito/data-tables';
import { lapToKhaiInitData, SearchFormValues } from './schema';
import LapToKhaiDialog from './components/lap-to-khai-dialog';
import SearchDialog from './components/search-dialog';
import ActionBar from './components/ActionBar';
import { useDialogState } from './hooks';
import { useTableData } from './hooks';

export default function ToKhaiThueGTGTPage() {
  const {
    initialSearchDialogOpen,
    showTable,
    lastSearchValues,
    isLapToKhaiDialogOpen,
    handleInitialSearchClose,
    handleInitialSearch,
    handleSearchClick,
    setShowTable,
    handleLapToKhaiDialogOpen,
    handleLapToKhaiDialogSubmit,
    handleLapToKhaiDialogClose
  } = useDialogState();

  const [selectedRow, setSelectedRow] = useState(null);

  const { tables, currentTable, handleRowClick, updateTableColumns, switchTable } = useTableData();

  function handleSearch(value: SearchFormValues) {
    handleInitialSearch(value);
  }

  const handleCoDinhCotClick = () => {
    console.log('Cố định cột clicked');
  };

  const handleChinhSuaMauInClick = () => {
    console.log('Chỉnh sửa mẫu in clicked');
  };

  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    // Khi user click vào tab trong AritoDataTables, chuyển đổi table tương ứng
    if (tables[newValue]) {
      switchTable(tables[newValue].id);
    }
  };

  return (
    <div className='flex h-full min-h-[calc(100vh-64px)] w-screen flex-col overflow-auto'>
      {showTable && (
        <div className='flex-1 overflow-hidden'>
          <ActionBar
            currentTable={currentTable}
            onSearchClick={handleSearchClick}
            onRefreshClick={() => {}}
            onLapToKhaiClick={handleLapToKhaiDialogOpen}
            onCoDinhCotClick={handleCoDinhCotClick}
            onChinhSuaMauInClick={handleChinhSuaMauInClick}
            searchParams={lastSearchValues}
          />
          <AritoDataTables
            tables={tables}
            onRowClick={setSelectedRow}
            onTabChange={handleTabChange}
            defaultTabIndex={tables.findIndex(table => table.id === currentTable.id)}
          />
        </div>
      )}

      <SearchDialog
        openSearchDialog={initialSearchDialogOpen}
        onCloseSearchDialog={handleInitialSearchClose}
        onSearch={handleSearch}
        updateTableColumns={() => {}}
      />

      <LapToKhaiDialog
        openDialog={isLapToKhaiDialogOpen}
        onCloseDialog={handleLapToKhaiDialogClose}
        onSubmit={handleLapToKhaiDialogSubmit}
        updateTableColumns={() => {}}
        initData={lapToKhaiInitData}
      />
    </div>
  );
}
