import { useState, useRef, useEffect } from 'react';
import { Group } from '@/types/schemas';

export const useSearch = () => {
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [isSearching, setIsSearching] = useState<boolean>(false);

  const searchTermRef = useRef<string>('');

  useEffect(() => {
    searchTermRef.current = searchTerm;
  }, [searchTerm]);

  const filterGroups = (groups: Group[]): Group[] => {
    if (!searchTermRef.current.trim()) {
      return groups;
    }

    const term = searchTermRef.current.toLowerCase().trim();
    return groups.filter(
      group =>
        group.ma_nhom.toLowerCase().includes(term) ||
        group.ten_phan_nhom.toLowerCase().includes(term) ||
        (group.ten2 && group.ten2.toLowerCase().includes(term))
    );
  };

  const handleSearchChange = (value: string) => {
    setSearchTerm(value);
    setIsSearching(!!value.trim());
  };

  const clearSearch = () => {
    setSearchTerm('');
    setIsSearching(false);
  };

  const showSearch = () => {
    setIsSearching(true);
  };

  return {
    searchTerm,
    isSearching,
    filterGroups,
    handleSearchChange,
    clearSearch,
    showSearch
  };
};
