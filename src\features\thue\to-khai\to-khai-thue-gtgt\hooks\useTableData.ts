import { useState } from 'react';
import {
  bangKeBanRaColumns,
  bangKeMuaVaoColumns,
  pl142Columns,
  vatTaxDeclarationColumns,
  vatTaxDeclarationPL43Columns
} from '../cols-definition';

export interface TableConfig {
  id: string;
  name: string;
  title: string;
  rows: any[];
  columns: any[];
  actionBarConfig: {
    showLapToKhai: boolean;
    showTinhLai: boolean;
    showCoDinhCot: boolean;
    showChinhSuaMauIn: boolean;
    showKetXuatDuLieu: boolean;
  };
}

export function useTableData() {
  const [currentTableId, setCurrentTableId] = useState('to-khai-gtgt');

  const [tables] = useState<TableConfig[]>([
    {
      id: 'to-khai-gtgt',
      name: 'Tờ khai thuế GTGT (01/GTGT)(TT80/2021)',
      title: 'Tờ khai thuế giá trị gia tăng',
      rows: [],
      columns: vatTaxDeclarationColumns,
      actionBarConfig: {
        showLapToKhai: true,
        showTinhLai: true,
        showCoDinhCot: false,
        showChinhSuaMauIn: false,
        showKetXuatDuLieu: false
      }
    },
    {
      id: 'to-khai-gtgt-pl43',
      name: 'Tờ khai thuế GTGT PL43',
      title: 'Tờ khai thuế GTGT PL43/2022/QH15',
      rows: [],
      columns: vatTaxDeclarationPL43Columns,
      actionBarConfig: {
        showLapToKhai: false,
        showTinhLai: false,
        showCoDinhCot: true,
        showChinhSuaMauIn: true,
        showKetXuatDuLieu: false
      }
    },
    {
      id: 'pl-giam-thue-gtgt',
      name: 'PL_GiamThue_GTGT_23_24',
      title: 'PL Giảm thuế GTGT 23-24',
      rows: [],
      columns: vatTaxDeclarationPL43Columns,
      actionBarConfig: {
        showLapToKhai: false,
        showTinhLai: false,
        showCoDinhCot: true,
        showChinhSuaMauIn: true,
        showKetXuatDuLieu: false
      }
    },
    {
      id: 'bang-ke-ban-ra',
      name: 'Bảng kê bán ra 01-1/GTGT',
      title: 'Bảng kê bán ra 01-1/GTGT',
      rows: [],
      columns: bangKeBanRaColumns,
      actionBarConfig: {
        showLapToKhai: false,
        showTinhLai: false,
        showCoDinhCot: true,
        showChinhSuaMauIn: false,
        showKetXuatDuLieu: true
      }
    },
    {
      id: 'to-khai-gtgt-pl142',
      name: 'Tờ khai thuế GTGT PL142',
      title: 'Tờ khai thuế GTGT PL142',
      rows: [],
      columns: pl142Columns,
      actionBarConfig: {
        showLapToKhai: false,
        showTinhLai: false,
        showCoDinhCot: true,
        showChinhSuaMauIn: true,
        showKetXuatDuLieu: false
      }
    },
    {
      id: 'bang-ke-mua-vao',
      name: 'Bảng kê mua vào 01-2/GTGT',
      title: 'Bảng kê mua vào 01-2/GTGT',
      rows: [],
      columns: bangKeMuaVaoColumns,
      actionBarConfig: {
        showLapToKhai: false,
        showTinhLai: false,
        showCoDinhCot: true,
        showChinhSuaMauIn: false,
        showKetXuatDuLieu: true
      }
    }
  ]);

  const currentTable = tables.find(table => table.id === currentTableId) || tables[0];

  const handleRowClick = (row: any) => {
    console.log('Row clicked:', row);
  };

  const updateTableColumns = (templateValue: string) => {
    console.log('Update table columns:', templateValue);
  };

  const switchTable = (tableId: string) => {
    setCurrentTableId(tableId);
  };

  return {
    tables,
    currentTable,
    currentTableId,
    handleRowClick,
    updateTableColumns,
    switchTable
  };
}
