// TypeScript interfaces for VatTuXuatSanXuatNhungChuaKhaiBaoDinhMuc

export interface VatTuXuatSanXuatNhungChuaKhaiBaoDinhMucItem {
  id: string;
  ma_chung_tu: string;
  ten_chung_tu: string;
  ngay_chung_tu: string;
  so_chung_tu: string;
  ma_vat_tu: string;
  ten_vat_tu: string;
  dien_giai: string;
}

export interface VatTuXuatSanXuatNhungChuaKhaiBaoDinhMucResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: VatTuXuatSanXuatNhungChuaKhaiBaoDinhMucItem[];
}

export interface SearchFormValues {
  donViChiNhanh?: any;
  ky?: number;
  nam?: number;
  [key: string]: any;
}

export interface UseVatTuXuatSanXuatNhungChuaKhaiBaoDinhMucReturn {
  data: VatTuXuatSanXuatNhungChuaKhaiBaoDinhMucItem[];
  isLoading: boolean;
  error: Error | null;
  fetchData: (searchParams: SearchFormValues) => Promise<void>;
  refreshData: () => Promise<void>;
}
