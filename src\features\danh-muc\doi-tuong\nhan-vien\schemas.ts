import { z } from 'zod';

export const nhanVienSchema = z.object({
  ma_nhan_vien: z.string().nonempty('<PERSON>ui lòng nhập mã nhân viên'),
  ho_ten_nhan_vien: z
    .string()
    .nonempty('<PERSON>ui lòng nhập họ và tên')
    .refine(val => /^[a-zA-ZÀ-ỹ\s]+$/.test(val), {
      message: 'Họ tên chỉ được chứa chữ cái và khoảng trắng'
    }),
  chuc_vu: z.string().nonempty('Vui lòng nhập chức vụ'),
  ma_bo_phan: z.string().optional(),
  gioi_tinh: z.string().nonempty('Vui lòng chọn giới tính'),
  ngay_sinh: z.string().optional(),
  noi_sinh: z.string().nonempty('<PERSON>ui lòng nhập nơi sinh'),
  dia_chi: z.string().nonempty('<PERSON>ui lòng nhập địa chỉ'),
  dien_thoai: z
    .string()
    .nonempty('<PERSON>ui lòng nhập số điện thoại')
    .refine(val => !val || /^\d{10}$/.test(val), {
      message: 'Số điện thoại phải có 10 chữ số'
    }),
  email: z.string().nonempty('Vui lòng nhập email').email('Email không hợp lệ'),
  so_cmnd: z.string().nonempty('Vui lòng nhập số CMND'),
  ngay_hieu_luc_cmnd: z.string().optional(),
  noi_cap_cmnd: z.string().optional(),
  nhan_vien_ban_hang: z.boolean().default(true),
  nhan_vien_mua_hang: z.boolean().default(true),
  cong_no_tam_ung: z.boolean().default(true)
});

export type NhanVienFormValues = z.infer<typeof nhanVienSchema>;

export const initialValues: NhanVienFormValues = {
  ma_nhan_vien: '',
  ho_ten_nhan_vien: '',
  chuc_vu: '',
  ma_bo_phan: '',
  gioi_tinh: '1',
  ngay_sinh: '',
  noi_sinh: '',
  dia_chi: '',
  dien_thoai: '',
  email: '',
  so_cmnd: '',
  ngay_hieu_luc_cmnd: '',
  noi_cap_cmnd: '',
  nhan_vien_ban_hang: true,
  nhan_vien_mua_hang: true,
  cong_no_tam_ung: true
};
