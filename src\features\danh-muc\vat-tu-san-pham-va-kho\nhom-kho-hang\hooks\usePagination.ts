import { useState, useRef, useEffect, useMemo } from 'react';
import { Group } from '@/types/schemas';

export const usePagination = () => {
  const [page, setPage] = useState<number>(0);
  const [pageSize, setPageSize] = useState<number>(10);

  const pageRef = useRef<number>(0);
  const pageSizeRef = useRef<number>(10);

  useEffect(() => {
    pageRef.current = page;
    pageSizeRef.current = pageSize;
  }, [page, pageSize]);

  const handlePageChange = (newPage: number) => {
    setPage(newPage);
  };

  const handlePageSizeChange = (newPageSize: number) => {
    setPageSize(newPageSize);
    setPage(0);
  };

  const getPaginatedData = (data: Group[]): Group[] => {
    const startIndex = pageRef.current * pageSizeRef.current;
    const endIndex = startIndex + pageSizeRef.current;
    return data.slice(startIndex, endIndex);
  };

  const paginationProps = useMemo(
    () => ({
      page,
      pageSize,
      rowCount: 0,
      onPageChange: handlePageChange,
      onPageSizeChange: handlePageSizeChange,
      pageSizeOptions: [5, 10, 25, 50, 100]
    }),
    [page, pageSize]
  );

  return {
    page,
    pageSize,
    handlePageChange,
    handlePageSizeChange,
    getPaginatedData,
    paginationProps
  };
};
