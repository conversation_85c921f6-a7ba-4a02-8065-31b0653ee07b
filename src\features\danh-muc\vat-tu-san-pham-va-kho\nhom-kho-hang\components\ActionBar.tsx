import { <PERSON><PERSON>, FileSearch, <PERSON>ader2, <PERSON><PERSON><PERSON>, Plus, RefreshCw, Search, Trash, List } from 'lucide-react';
import { AritoActionButton } from '@/components/custom/arito/action-button';
import AritoMenuButton from '@/components/custom/arito/menu-button';
import AritoActionBar from '@/components/custom/arito/action-bar';
import AritoIcon from '@/components/custom/arito/icon';
import { groupTypes } from '..';

interface ActionBarProps {
  onAddClick: () => void;
  onEditClick: () => void;
  onDeleteClick: () => void;
  onCopyClick: () => void;
  isEditDisabled?: boolean;
  onViewClick: () => void;
  isViewDisabled?: boolean;
  activeGroup: string;
  isLoading?: boolean;
  onRefresh?: () => void;
  onSearch?: () => void;
  onExport?: () => void;
  onImportFromExcel?: () => void;
  onDownloadTemplate?: () => void;
  onBulkAction?: () => void;
  selectedItemsCount?: number;
}

export const ActionBar = ({
  onAddClick,
  onEditClick,
  isEditDisabled = true,
  onViewClick,
  onDeleteClick,
  onCopyClick,
  isViewDisabled = true,
  activeGroup,
  isLoading = false,
  onRefresh,
  onSearch,
  onExport,
  onImportFromExcel,
  onDownloadTemplate,
  onBulkAction,
  selectedItemsCount = 0
}: ActionBarProps) => {
  const activeGroupInfo = groupTypes.find(group => group.value === activeGroup);
  const displayText = activeGroupInfo
    ? `Phân nhóm: ${activeGroupInfo.shortCode} - ${activeGroupInfo.label}`
    : 'Phân nhóm KHOHANG - Nhóm kho hàng';

  return (
    <AritoActionBar
      titleComponent={
        <div>
          <h1 className='text-xl font-bold'>Danh mục nhóm kho hàng</h1>
          <div className='text-[10px] text-gray-500'>
            <p>
              <span className='mr-2 inline-block h-2 w-2 rounded-full bg-red-600'></span>
              <span className='font-semibold'>{displayText}</span>
            </p>
          </div>
        </div>
      }
    >
      {isLoading && <Loader2 className='mr-2 h-5 w-5 animate-spin text-gray-500' />}

      {onAddClick && <AritoActionButton title='Thêm' icon={Plus} onClick={onAddClick} disabled={isLoading} />}
      {onEditClick && (
        <AritoActionButton title='Sửa' icon={Pencil} onClick={onEditClick} disabled={isViewDisabled || isLoading} />
      )}
      {onDeleteClick && (
        <AritoActionButton title='Xóa' icon={Trash} onClick={onDeleteClick} disabled={isViewDisabled || isLoading} />
      )}
      {onCopyClick && (
        <AritoActionButton title='Sao chép' icon={Copy} onClick={onCopyClick} disabled={isViewDisabled || isLoading} />
      )}
      {onViewClick && (
        <AritoActionButton title='Xem' icon={FileSearch} onClick={onViewClick} disabled={isViewDisabled || isLoading} />
      )}
      {onBulkAction && selectedItemsCount > 0 && (
        <AritoActionButton
          title={`Thao tác hàng loạt (${selectedItemsCount})`}
          icon={List}
          onClick={onBulkAction}
          disabled={isLoading}
        />
      )}
      <AritoMenuButton
        items={[
          {
            title: 'Tìm kiếm',
            icon: <Search className='h-4 w-4' />,
            onClick: onSearch || (() => {}),
            group: 0
          },
          {
            title: 'Làm mới',
            icon: <RefreshCw className='h-4 w-4' />,
            onClick: onRefresh || (() => {}),
            group: 1
          },
          {
            title: 'Cố định cột',
            icon: <AritoIcon icon={16} />,
            group: 1
          },
          {
            title: 'Kết xuất dữ liệu',
            icon: <AritoIcon icon={555} />,
            onClick: onExport || (() => {}),
            group: 2
          },
          {
            title: 'Tải mẫu Excel',
            icon: <AritoIcon icon={28} />,
            onClick: onDownloadTemplate || (() => {}),
            group: 3
          },
          {
            title: 'Lấy dữ liệu từ Excel',
            icon: <AritoIcon icon={29} />,
            onClick: onImportFromExcel || (() => {}),
            group: 3
          }
        ]}
      />
    </AritoActionBar>
  );
};
