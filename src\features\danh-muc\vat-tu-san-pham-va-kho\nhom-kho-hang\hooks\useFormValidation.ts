import { useState, useRef, useEffect } from 'react';
import { z } from 'zod';
import { warehouseGroupSchema } from '../schemas';

type ValidationErrors = {
  [key: string]: string[];
};

export const useFormValidation = () => {
  const [errors, setErrors] = useState<ValidationErrors>({});
  const [isValid, setIsValid] = useState<boolean>(true);
  const errorsRef = useRef<ValidationErrors>({});

  useEffect(() => {
    errorsRef.current = errors;
  }, [errors]);

  const validateForm = (data: any): boolean => {
    try {
      warehouseGroupSchema.parse(data);

      setErrors({});
      setIsValid(true);

      return true;
    } catch (error) {
      if (error instanceof z.ZodError) {
        const formattedErrors: ValidationErrors = {};

        error.errors.forEach(err => {
          const path = err.path.join('.');

          if (!formattedErrors[path]) {
            formattedErrors[path] = [];
          }

          formattedErrors[path].push(err.message);
        });

        setErrors(formattedErrors);
        setIsValid(false);
      } else {
        console.error('Validation error:', error);
        setIsValid(false);
      }

      return false;
    }
  };

  const getFieldError = (field: string): string | undefined => {
    return errorsRef.current[field]?.[0];
  };

  const hasFieldError = (field: string): boolean => {
    return !!errorsRef.current[field]?.length;
  };

  const clearErrors = () => {
    setErrors({});
    setIsValid(true);
  };

  const setFieldError = (field: string, message: string) => {
    setErrors(prev => ({
      ...prev,
      [field]: [message]
    }));
    setIsValid(false);
  };

  return {
    errors,
    isValid,
    validateForm,
    getFieldError,
    hasFieldError,
    clearErrors,
    setFieldError
  };
};
