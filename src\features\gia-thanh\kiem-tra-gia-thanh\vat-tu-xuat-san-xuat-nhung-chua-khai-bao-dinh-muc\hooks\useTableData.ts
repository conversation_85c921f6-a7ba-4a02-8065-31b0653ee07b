import { useMemo } from 'react';
import { TableData } from '@/components/custom/arito/data-tables/types';
import { VatTuXuatSanXuatNhungChuaKhaiBaoDinhMucItem } from '../types';

export interface UseTableDataReturn {
  tables: TableData[];
  handleRowClick: (params: any) => void;
}

export function useTableData(data: VatTuXuatSanXuatNhungChuaKhaiBaoDinhMucItem[]): UseTableDataReturn {
  const tables = useMemo(() => {
    console.log('🔄 useTableData: Data changed, updating tables. Data length:', data.length);
    console.log('🔄 useTableData: Data content:', data);

    const tableData: TableData[] = [
      {
        name: '',
        columns: [
          {
            field: 'ma_chung_tu',
            headerName: 'Mã chứng từ',
            width: 120,
            renderCell: (params: any) => {
              console.log('params: ', params);
              return params.row.ma_chung_tu || '';
            }
          },
          { field: 'ten_chung_tu', headerName: 'Tên chứng từ', width: 150 },
          { field: 'ngay_chung_tu', headerName: 'Ngày c/từ', width: 120 },
          { field: 'so_chung_tu', headerName: 'Số c/từ', width: 120 },
          { field: 'ma_vat_tu', headerName: 'Mã vật tư', width: 120 },
          { field: 'ten_vat_tu', headerName: 'Tên vật tư', width: 200 },
          { field: 'dien_giai', headerName: 'Diễn giải', width: 250 }
        ],
        rows: data.map(item => ({
          id: item.id,
          ma_chung_tu: item.ma_chung_tu,
          ten_chung_tu: item.ten_chung_tu,
          ngay_chung_tu: item.ngay_chung_tu,
          so_chung_tu: item.so_chung_tu,
          ma_vat_tu: item.ma_vat_tu,
          ten_vat_tu: item.ten_vat_tu,
          dien_giai: item.dien_giai
        }))
      }
    ];

    console.log('📋 useTableData: Updated tables:', tableData);
    console.log('📋 useTableData: Table rows count:', tableData[0].rows.length);

    return tableData;
  }, [data]);

  const handleRowClick = (params: any) => {
    console.log('Row clicked:', params);
  };

  return {
    tables,
    handleRowClick
  };
}
