import React, { useState } from 'react';

import { Button } from '@mui/material';
import { AritoDialog } from '@/components/custom/arito/dialog';
import { AritoForm } from '@/components/custom/arito/form';
import { searchSchema, initialValues } from '../schema';
import AritoIcon from '@/components/custom/arito/icon';
import BasicInfo from './BasicInfo';

interface InitialSearchDialogProps {
  open: boolean;
  onClose: () => void;
  onSearch: (values: any) => void;
}

export const InitialSearchDialog: React.FC<InitialSearchDialogProps> = ({ open, onClose, onSearch }) => {
  const [formValues, setFormValues] = useState(initialValues);

  const handleSubmit = (data: any) => {
    // The don_vi field is now directly handled by the FormField select
    onSearch(data);
  };

  const handleDirectSubmit = () => {
    // The form values already include the don_vi field from the select
    onSearch(formValues);
    onClose();
  };

  return (
    <AritoDialog
      open={open}
      onClose={onClose}
      title='Vật tư xuất sản xuất nhưng chưa khai báo định mức'
      maxWidth='lg'
      disableBackdropClose={false}
      disableEscapeKeyDown={false}
      titleIcon={<AritoIcon icon={12} />}
      bottomBar={
        <>
          <Button variant='contained' onClick={handleDirectSubmit} style={{ backgroundColor: '#26827C' }}>
            <AritoIcon icon={884} />
            <span className='ml-1'>Đồng ý</span>
          </Button>
          <Button onClick={onClose} variant='outlined'>
            <AritoIcon icon={885} />
            <span className='ml-1'>Huỷ</span>
          </Button>
        </>
      }
    >
      <AritoForm
        mode='add'
        hasAritoActionBar={false}
        schema={searchSchema}
        initialData={initialValues}
        onSubmit={data => {
          setFormValues(data);
          handleSubmit(data);
        }}
        className='w-full'
        headerFields={<BasicInfo />}
      />
    </AritoDialog>
  );
};

export default InitialSearchDialog;
