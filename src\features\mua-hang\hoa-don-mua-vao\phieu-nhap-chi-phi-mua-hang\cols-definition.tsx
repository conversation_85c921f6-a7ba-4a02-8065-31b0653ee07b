import { GridColDef, GridRenderCellParams } from '@mui/x-data-grid';

export const getPhieuNhapChiPhiMuaHangColumns = (
  handleOpenViewForm: (obj: any) => void,
  handleOpenEditForm: (obj: any) => void
): GridColDef[] => [
  { field: 'trang_thai', headerName: 'Trạng thái', width: 120 },
  {
    field: 'so_ctu',
    headerName: 'Số c/từ',
    width: 100,
    renderCell: (params: GridRenderCellParams) => (
      <div className='cursor-pointer text-teal-600 hover:underline' onClick={() => handleOpenViewForm(params.row)}>
        {params.value}
      </div>
    )
  },
  { field: 'ngay_ctu', headerName: 'Ngày c/từ', width: 120, type: 'date' },
  { field: 'ma_khach_hang', headerName: 'M<PERSON> khách hàng', width: 150 },
  { field: 'ten_khach_hang', headerName: 'Tên khách hàng', width: 200 },
  { field: 'dien_giai', headerName: 'Diễn giải', width: 200 },
  { field: 'tai_khoan', headerName: 'Tài khoản', width: 120 },
  { field: 'tong_tien', headerName: 'Tổng tiền', width: 120, type: 'number' },
  { field: 'ngoai_te', headerName: 'Ngoại tệ', width: 100 }
];

export const phieuNhapChiPhiMuaHangItemColumns: GridColDef[] = [
  { field: 'ma_san_pham', headerName: 'Mã sản phẩm', width: 150 },
  { field: 'ten_san_pham', headerName: 'Tên sản phẩm', width: 200 },
  { field: 'dvt', headerName: 'Đvt', width: 100 },
  { field: 'ma_kho', headerName: 'Mã kho', width: 150 },
  { field: 'so_luong', headerName: 'Số lượng', width: 120, type: 'number' },
  { field: 'tien_hang', headerName: 'Tiền hàng', width: 150, type: 'number' },
  { field: 'tien_tinh_thue_nk', headerName: 'Tiền tính thuế nk', width: 180, type: 'number' },
  { field: 'chi_phi', headerName: 'Chi phí %s', width: 150 },
  { field: 'tk_no', headerName: 'Tk nợ', width: 120 },
  { field: 'chi_phi_tong', headerName: 'Chi phí', width: 150, type: 'number' },
  { field: 'thue_gtgt_phan_tram', headerName: 'Thuế GTGT %s', width: 150 },
  { field: 'thue_gtgt', headerName: 'Thuế giá GTGT', width: 150, type: 'number' },
  { field: 'bo_phan', headerName: 'Bộ phận', width: 150 },
  { field: 'vu_viec', headerName: 'Vụ việc', width: 150 },
  { field: 'hop_dong', headerName: 'Hợp đồng', width: 150 },
  { field: 'dot_thanh_toan', headerName: 'Đợt thanh toán', width: 150 },
  { field: 'khe_uoc', headerName: 'Khế ước', width: 150 },
  { field: 'phi', headerName: 'Phí', width: 120 },
  { field: 'san_pham', headerName: 'Sản phẩm', width: 150 },
  { field: 'lenh_san_xuat', headerName: 'Lệnh sản xuất', width: 150 },
  { field: 'cp_khong_hop_le', headerName: 'C/p không h/lệ', width: 180 },
  { field: 'so_hd_mua_trong_nuoc', headerName: 'Số hóa đơn mua trong nước', width: 200 },
  { field: 'so_hd_mua_nhap_khau', headerName: 'Số hóa đơn mua nhập khẩu', width: 200 },
  { field: 'so_hd_mua_xuat_thang', headerName: 'Số hóa đơn mua xuất thẳng', width: 200 },
  { field: 'dong_hd', headerName: 'Dòng HĐ', width: 150 }
];

export const phieuNhapChiPhiMuaHangChiTietTabColumns: GridColDef[] = [
  { field: 'ma_san_pham', headerName: 'Mã sản phẩm', width: 150 },
  { field: 'ten_san_pham', headerName: 'Tên sản phẩm', width: 200 },
  { field: 'dvt', headerName: 'Đvt', width: 100 },
  { field: 'ma_kho', headerName: 'Mã kho', width: 150 },
  { field: 'so_luong', headerName: 'Số lượng', width: 120, type: 'number' },
  { field: 'tien_hang', headerName: 'Tiền hàng', width: 150, type: 'number' },
  { field: 'tien_tinh_thue_nk', headerName: 'Tiền tính thuế nk', width: 180, type: 'number' },
  { field: 'chi_phi_vnd', headerName: 'Chi phí VND', width: 150 },
  { field: 'tk_no', headerName: 'Tk nợ', width: 120 },
  { field: 'thue_gtgt_vnd', headerName: 'Thuế GTGT VND', width: 150, type: 'number' },
  { field: 'bo_phan', headerName: 'Bộ phận', width: 150 },
  { field: 'vu_viec', headerName: 'Vụ việc', width: 150 },
  { field: 'hop_dong', headerName: 'Hợp đồng', width: 150 },
  { field: 'dot_thanh_toan', headerName: 'Đợt thanh toán', width: 150 },
  { field: 'khe_uoc', headerName: 'Khế ước', width: 150 },
  { field: 'phi', headerName: 'Phí', width: 120 },
  { field: 'san_pham', headerName: 'Sản phẩm', width: 150 },
  { field: 'lenh_san_xuat', headerName: 'Lệnh sản xuất', width: 150 },
  { field: 'cp_khong_hop_le', headerName: 'C/p không h/lệ', width: 180 },
  { field: 'so_hd_mua_trong_nuoc', headerName: 'Số hóa đơn mua trong nước', width: 200 },
  { field: 'so_hd_mua_nhap_khau', headerName: 'Số hóa đơn mua nhập khẩu', width: 200 },
  { field: 'so_hd_mua_xuat_thang', headerName: 'Số hóa đơn mua xuất thẳng', width: 200 },
  { field: 'dong_hd', headerName: 'Dòng HĐ', width: 150 }
];

export const phieuNhapChiPhiMuaHangChiPhiTabColumns: GridColDef[] = [
  { field: 'ma_chi_phi', headerName: 'Mã chi phí', width: 150 },
  { field: 'ten_chi_phi', headerName: 'Tên chi phí', width: 200 },
  { field: 'tien_vnd', headerName: 'Tiền VND', width: 100 },
  { field: 'tieu_thuc_phan_bo', headerName: 'Tiêu thức phân bổ', width: 100 }
];

export const phieuNhapChiPhiMuaHangChiPhiChiTietTabColumns: GridColDef[] = [
  { field: 'ma_chi_phi', headerName: 'Mã chi phí', width: 150 },
  { field: 'ma_san_pham', headerName: 'Mã sản phẩm', width: 200 },
  { field: 'tien_vnd', headerName: 'Tiền VND', width: 100 },
  { field: 'dong_chi_tiet', headerName: 'Dòng chi tiết', width: 100 }
];

export const phieuNhapChiPhiMuaHangThueTabColumns: GridColDef[] = [
  { field: 'so_hoa_don', headerName: 'Số hóa đơn', width: 150 },
  { field: 'ky_hieu', headerName: 'Ký hiệu', width: 100 },
  { field: 'ngay_hoa_don', headerName: 'Ngày hóa đơn', width: 120, type: 'date' },
  { field: 'thue_suat', headerName: 'Thuế suất', width: 100 },
  { field: 'mau_hoa_don', headerName: 'Mẫu hóa đơn', width: 150 },
  { field: 'mau_bao_cao', headerName: 'Mẫu báo cáo', width: 150 },
  { field: 'ma_tinh_chat', headerName: 'Mã tính chất', width: 150 },
  { field: 'ma_ncc', headerName: 'Mã ncc', width: 150 },
  { field: 'ten_nha_cung_cap', headerName: 'Tên nhà cung cấp', width: 200 },
  { field: 'dia_chi', headerName: 'Địa chỉ', width: 200 },
  { field: 'ma_so_thue', headerName: 'Mã số thuế', width: 150 },
  { field: 'ten_hang_hoa_dich_vu', headerName: 'Tên hàng hóa - dịch vụ', width: 200 },
  { field: 'tien_hang_vnd', headerName: 'Tiền hàng VND', width: 150, type: 'number' },
  { field: 'tk_thue', headerName: 'Tk thuế', width: 120 },
  { field: 'thue_vnd', headerName: 'Thuế VND', width: 150, type: 'number' },
  { field: 'cuc_thue', headerName: 'Cục thuế', width: 150 },
  { field: 'ma_thanh_toan', headerName: 'Mã thanh toán', width: 150 },
  { field: 'ghi_chu', headerName: 'Ghi chú', width: 200 },
  { field: 'bo_phan', headerName: 'Bộ phận', width: 150 },
  { field: 'vu_viec', headerName: 'Vụ việc', width: 150 },
  { field: 'hop_dong', headerName: 'Hợp đồng', width: 150 },
  { field: 'dot_thanh_toan', headerName: 'Đợt thanh toán', width: 150 },
  { field: 'khe_uoc', headerName: 'Khế ước', width: 150 },
  { field: 'phi', headerName: 'Phí', width: 120 },
  { field: 'san_pham', headerName: 'Sản phẩm', width: 150 },
  { field: 'lenh_san_xuat', headerName: 'Lệnh sản xuất', width: 150 },
  { field: 'cp_khong_hop_le', headerName: 'C/p không h/lệ', width: 180 }
];

export const hoaDonMuaItemColumns: GridColDef[] = [
  { field: 'ma_san_pham', headerName: 'Mã sản phẩm', width: 150 },
  { field: 'ten_san_pham', headerName: 'Tên sản phẩm', width: 200 },
  { field: 'dvt', headerName: 'Đvt', width: 100 },
  { field: 'chon', headerName: 'Chọn', width: 100 },
  { field: 'so_luong', headerName: 'Số lượng', width: 120, type: 'number' },
  { field: 'ma_kho', headerName: 'Mã kho', width: 150 },
  { field: 'ma_lo', headerName: 'Mã lô', width: 150 },
  { field: 'ma_vi_tri', headerName: 'Mã vị trí', width: 150 },
  { field: 'tk_kho', headerName: 'Tk kho', width: 120 },
  { field: 'bo_phan', headerName: 'Bộ phận', width: 150 },
  { field: 'vu_viec', headerName: 'Vụ việc', width: 150 },
  { field: 'hop_dong', headerName: 'Hợp đồng', width: 150 },
  { field: 'dot_thanh_toan', headerName: 'Đợt thanh toán', width: 150 },
  { field: 'khe_uoc', headerName: 'Khế ước', width: 150 },
  { field: 'phi', headerName: 'Phí', width: 120 },
  { field: 'san_pham', headerName: 'Sản phẩm', width: 150 },
  { field: 'lenh_san_xuat', headerName: 'Lệnh sản xuất', width: 150 },
  { field: 'cp_khong_hop_le', headerName: 'C/p không h/lệ', width: 180 },
  { field: 'so_hd_mua_trong_nuoc', headerName: 'Số hóa đơn mua trong nước', width: 200 },
  { field: 'so_hd_mua_nhap_khau', headerName: 'Số hóa đơn mua nhập khẩu', width: 200 },
  { field: 'so_hd_mua_xuat_thang', headerName: 'Số hóa đơn mua xuất thẳng', width: 200 },
  { field: 'dong_hd', headerName: 'Dòng HĐ', width: 150 }
];

export const getInfoItemColumns: GridColDef[] = [
  { field: 'ma_san_pham', headerName: 'Mã sản phẩm', width: 150 },
  { field: 'ten_san_pham', headerName: 'Tên sản phẩm', width: 200 },
  { field: 'dvt', headerName: 'Đvt', width: 100 },
  { field: 'chon', headerName: 'Chọn', width: 100 },
  { field: 'so_luong', headerName: 'Số lượng', width: 120, type: 'number' },
  { field: 'ma_kho', headerName: 'Mã kho', width: 150 },
  { field: 'ma_lo', headerName: 'Mã lô', width: 150 },
  { field: 'ma_vi_tri', headerName: 'Mã vị trí', width: 150 },
  { field: 'tk_kho', headerName: 'Tk kho', width: 120 },
  { field: 'bo_phan', headerName: 'Bộ phận', width: 150 },
  { field: 'vu_viec', headerName: 'Vụ việc', width: 150 },
  { field: 'hop_dong', headerName: 'Hợp đồng', width: 150 },
  { field: 'dot_thanh_toan', headerName: 'Đợt thanh toán', width: 150 },
  { field: 'khe_uoc', headerName: 'Khế ước', width: 150 },
  { field: 'phi', headerName: 'Phí', width: 120 },
  { field: 'san_pham', headerName: 'Sản phẩm', width: 150 },
  { field: 'lenh_san_xuat', headerName: 'Lệnh sản xuất', width: 150 },
  { field: 'cp_khong_hop_le', headerName: 'C/p không h/lệ', width: 180 },
  { field: 'so_hd_mua_trong_nuoc', headerName: 'Số hóa đơn mua trong nước', width: 250 },
  { field: 'so_hd_mua_nhap_khau', headerName: 'Số hóa đơn mua nhập khẩu', width: 250 },
  { field: 'so_hd_mua_xuat_thang', headerName: 'Số hóa đơn mua xuất thẳng', width: 250 },
  { field: 'dong_hd', headerName: 'Dòng HĐ', width: 120 }
];

export const NhaCungCapSearchColDef: GridColDef[] = [
  { field: 'ma_doi_tuong', headerName: 'Mã đối tượng', flex: 1 },
  { field: 'ten_doi_tuong', headerName: 'Tên đối tượng', flex: 2 },
  { field: 'cong_no_phai_thu', headerName: 'Công nợ p/thu', flex: 2 },
  { field: 'cong_no_phai_tra', headerName: 'Công nợ p/trả', flex: 1 },
  { field: 'ma_so_thue', headerName: 'Mã số thuế', flex: 1 },
  { field: 'email', headerName: 'Email', flex: 1 }
];

export const TaiKhoanSearchColDef: GridColDef[] = [
  { field: 'ma_tai_khoan', headerName: 'Mã tài khoản', flex: 1 },
  { field: 'ten_tai_khoan', headerName: 'Tên tài khoản', flex: 2 },
  { field: 'tai_khoan_me', headerName: 'Tài khoản mẹ', flex: 1 },
  { field: 'tai_khoan_so_cai', headerName: 'Tk sổ cái', flex: 1 },
  { field: 'tai_khoan_chi_tiet', headerName: 'Tk chi tiết', flex: 1 },
  { field: 'ban_tai_khoan', headerName: 'Bậc tài khoản', flex: 1 }
];

export const HanThanhToanSearchColDef: GridColDef[] = [
  { field: 'ma_dktt', headerName: 'Mã ĐKTT', flex: 1 },
  { field: 'ten_dktt', headerName: 'Tên điều khoản TT', flex: 2 },
  { field: 'so_ngay_no', headerName: 'Số ngày nợ', flex: 1 }
];

export const QuyenSoChungTuSearchColDef: GridColDef[] = [
  { field: 'ma_quyen', headerName: 'Mã quyển', flex: 1 },
  { field: 'ten_quyen', headerName: 'Tên quyển', flex: 2 },
  { field: 'so_khai_bao', headerName: 'Số khai báo', flex: 1 }
];

export const SoHoaDonMuaSearchColDef: GridColDef[] = [
  { field: 'so_chung_tu', headerName: 'Số c/từ', flex: 1 },
  { field: 'ngay_chung_tu', headerName: 'Ngày c/từ', flex: 1 },
  { field: 'ma_doi_tuong', headerName: 'Mã đối tượng', flex: 2 },
  { field: 'ten_doi_tuong', headerName: 'Tên đối tượng', flex: 3 },
  { field: 'ngoai_te', headerName: 'Ngoại tệ', flex: 1 },
  { field: 'tong_tien', headerName: 'Tổng tiền', flex: 1 }
];

export const MaVatTuSearchCol: GridColDef[] = [
  { field: 'ma_vat_tu', headerName: 'Mã vật tư', width: 150 },
  { field: 'ten_vat_tu', headerName: 'Tên vật tư', width: 250 },
  { field: 'dvt', headerName: 'Đvt', width: 80 },
  { field: 'nhom_1', headerName: 'Nhóm 1', width: 120 },
  { field: 'theo_doi_lo', headerName: 'Theo dõi lô', width: 120 },
  { field: 'quy_cach', headerName: 'Quy cách', width: 150 },
  { field: 'hinh_anh', headerName: 'Hình ảnh', width: 100 }
];

export const MaKhoSearchCol: GridColDef[] = [
  { field: 'ma_kho', headerName: 'Mã kho', width: 150 },
  { field: 'ten_kho', headerName: 'Tên kho', width: 200 },
  { field: 'don_vi', headerName: 'Đơn vị', width: 200 },
  { field: 'theo_doi_vi_tri', headerName: 'Theo dõi vị trí', width: 120 }
];

export const MaViTriSearchCol: GridColDef[] = [
  { field: 'ma_vi_tri', headerName: 'Mã vị trí', width: 150 },
  { field: 'ten_vi_tri', headerName: 'Tên vị trí', width: 250 }
];

export const DonViTinhCol: GridColDef[] = [
  { field: 'ma_don_vi_tinh', headerName: 'Đvt', width: 150, editable: true },
  { field: 'ten_don_vi_tinh', headerName: 'Tên đơn vị tính', width: 200, editable: true },
  { field: 'id', headerName: 'ID', width: 100, editable: true }
];

export const MaLoColDef: GridColDef[] = [
  { field: 'ma_lo', headerName: 'Mã lô', flex: 1 },
  { field: 'ten_lo', headerName: 'Tên lô', flex: 2 }
];
