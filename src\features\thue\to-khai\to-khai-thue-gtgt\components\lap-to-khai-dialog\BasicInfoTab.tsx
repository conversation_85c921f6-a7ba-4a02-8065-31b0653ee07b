import React from 'react';
import { SearchField } from '@/components/custom/arito/form/custom-search-field';
import { FormField } from '@/components/custom/arito/form/form-field';
import { quyenChungTuSearchColumns } from '../../cols-definition';
import { Label } from '@/components/ui/label';
import { QUERY_KEYS } from '@/constants';

interface BasicInfoTabProps {
  formMode: 'add' | 'edit' | 'view';
}

const BasicInfoTab: React.FC<BasicInfoTabProps> = ({ formMode }) => {
  return (
    <div className='space-y-4 p-4'>
      <div className='space-y-4'>
        {/* Loại */}
        <div className='flex items-center'>
          <Label className='w-32 min-w-32 text-sm font-medium'>Loại</Label>
          <FormField name='loai' type='text' className='w-96' placeholder='Tờ khai thuế GTGT' disabled={true} />
        </div>

        {/* Đơn vị */}
        <div className='flex items-center'>
          <Label className='w-32 min-w-32 text-sm font-medium'>Đơn vị</Label>
          <FormField
            name='don_vi'
            type='text'
            className='w-[520px]'
            placeholder='CÔNG TY CỔ PHẦN SẢN XUẤT THƯƠNG MẠI'
            disabled={true}
          />
        </div>

        {/* Quyển/Số CT */}
        <div className='flex items-center'>
          <Label className='w-32 min-w-32 text-sm font-medium'>Quyển/Số CT</Label>
          <SearchField
            name='quyen_ct'
            type='text'
            displayRelatedField={'ten_quyen'}
            columnDisplay={'ma_quyen'}
            className='w-20'
            searchEndpoint={`/${QUERY_KEYS.QUYEN_CHUNG_TU}`}
            searchColumns={quyenChungTuSearchColumns}
            dialogTitle='Danh mục quyển'
            placeholder='TKT'
            disabled={formMode === 'view'}
          />
          <FormField
            name='so_ct'
            type='text'
            className='w-60'
            placeholder='TK.25.05.001'
            disabled={formMode === 'view'}
          />
        </div>

        {/* Ngày lập */}
        <div className='flex items-center'>
          <Label className='w-32 min-w-32 text-sm font-medium'>Ngày lập</Label>
          <FormField name='ngay_ct' type='date' className='w-40' disabled={formMode === 'view'} />
        </div>

        {/* Diễn giải */}
        <div className='flex items-center'>
          <Label className='w-32 min-w-32 text-sm font-medium'>Diễn giải</Label>
          <FormField
            name='dien_giai'
            type='text'
            className='w-80'
            placeholder='Tờ khai thuế GTGT Tháng 4'
            disabled={formMode === 'view'}
          />
        </div>

        {/* Trạng thái */}
        <div className='flex items-center'>
          <Label className='w-32 min-w-32 text-sm font-medium'>Trạng thái</Label>
          <FormField
            name='trang_thai'
            type='select'
            className='w-60'
            options={[
              { value: 'da_lap_to_khai', label: 'Đã lập tờ khai' },
              { value: 'chua_lap_to_khai', label: 'Chưa lập tờ khai' }
            ]}
            disabled={formMode === 'view'}
          />
        </div>

        {/* Thông tin tờ khai - Hidden, moved to separate section */}
        <div className='hidden'>
          <FormField
            name='thoi_gian_to_khai'
            type='select'
            label='Thời gian tờ khai'
            labelClassName='text-sm min-w-32'
            className='w-48'
            options={[
              { value: 'to_khai_thang', label: 'Tờ khai tháng' },
              { value: 'to_khai_quy', label: 'Tờ khai quý' },
              { value: 'to_khai_nam', label: 'Tờ khai năm' }
            ]}
            disabled={formMode === 'view'}
          />

          <div className='flex space-x-4'>
            <FormField
              name='thang'
              type='number'
              label='Tháng'
              labelClassName='text-sm min-w-16'
              className='w-20'
              disabled={formMode === 'view'}
            />

            <FormField
              name='nam'
              type='number'
              label='Năm'
              labelClassName='text-sm min-w-16'
              className='w-24'
              disabled={formMode === 'view'}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default BasicInfoTab;
