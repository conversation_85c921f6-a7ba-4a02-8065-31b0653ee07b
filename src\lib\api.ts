import axios, { AxiosRequestConfig, AxiosResponse } from 'axios';
import MockAdapter from 'axios-mock-adapter';

interface ApiConfig<T = any> extends AxiosRequestConfig {
  mock?: boolean;
  mockData?: T[];
}

// Create base axios instance
const baseApi = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_URL || 'https://be-dev.ttmi.pro/api',
  headers: {
    'Content-Type': 'application/json'
  }
});

console.log('API URL:', process.env.NEXT_PUBLIC_API_URL || 'https://be-dev.ttmi.pro/api');

class EnhancedAxiosInstance {
  private instance = baseApi;
  private mockAdapter: MockAdapter | null = null;

  private setupMockAdapter(_config: ApiConfig) {
    if (!this.mockAdapter) {
      this.mockAdapter = new MockAdapter(this.instance, {
        delayResponse: 300
      });
    }
    return this.mockAdapter;
  }

  private createMockResponse<T>(mockData?: T[], _url?: string, _method?: string): any {
    if (mockData && mockData.length > 0) {
      const response = {
        count: mockData.length,
        next: null,
        previous: null,
        results: mockData
      };
      return response;
    }

    const emptyResponse = {
      count: 0,
      next: null,
      previous: null,
      results: []
    };
    return emptyResponse;
  }

  async get<T = any>(url: string, config?: ApiConfig): Promise<AxiosResponse<T>> {
    if (config?.mock === true) {
      const mock = this.setupMockAdapter(config);
      const mockResponse = this.createMockResponse(config.mockData, url, 'GET');

      mock.onGet(url).reply(200, mockResponse);

      try {
        const { mock: _, mockData: __, ...cleanConfig } = config;
        const response = await this.instance.get<T>(url, cleanConfig);
        mock.reset();
        return response;
      } catch (error) {
        mock.reset();
        throw error;
      }
    }
    return this.instance.get<T>(url, config);
  }

  async post<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.instance.post<T>(url, data, config);
  }

  async put<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.instance.put<T>(url, data, config);
  }

  async delete<T = any>(url: string, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.instance.delete<T>(url, config);
  }

  async patch<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.instance.patch<T>(url, data, config);
  }

  get interceptors() {
    return this.instance.interceptors;
  }
  resetMock() {
    if (this.mockAdapter) {
      this.mockAdapter.reset();
    }
  }
}

// Create enhanced API instance
const api = new EnhancedAxiosInstance();

// Add request interceptor to include auth token
api.interceptors.request.use(
  config => {
    const token = localStorage.getItem('auth_token');
    if (token) {
      config.headers['Authorization'] = `Token ${token}`;
    }
    return config;
  },
  error => {
    return Promise.reject(error);
  }
);

// Add response interceptor to handle errors
api.interceptors.response.use(
  response => response,
  error => {
    if (error.response?.status === 401) {
      // Clear token and redirect to login on auth error
      localStorage.removeItem('auth_token');
      window.location.href = '/xac-thuc';
    }
    return Promise.reject(error);
  }
);

export default api;
