'use client';

import AritoDataTables from '@/components/custom/arito/data-tables';
import { ActionBar, FormDialog, ConfirmDialog } from './components';
import { LoadingOverlay } from '@/components/custom/arito';
import { factorTypeColumns } from './cols-definition';
import { LoaiYeuToInput } from '@/types/schemas';
import { initialFormValues } from './schema';
import { toast } from '@/hooks/use-toast';
import { useLoaiYeuTo } from '@/hooks';
import { useFormState } from '@/hooks';

export default function LoaiYeuToPage() {
  const { loaiYeuTos, isLoading, addLoaiYeuTo, updateLoaiYeuTo, deleteLoaiYeuTo, refreshLoaiYeuTos } = useLoaiYeuTo();

  const {
    showForm,
    showDelete,
    formMode,
    selectedObj,
    selectedRowIndex,
    isCopyMode,

    handleCloseForm,
    handleCloseDelete,
    handleAddClick,
    handleEditClick,
    handleViewClick,
    handleDeleteClick,
    handleCopyClick,
    handleRowClick,
    clearSelection
  } = useFormState();

  const handleSubmit = async (data: LoaiYeuToInput) => {
    try {
      if (formMode === 'add') {
        await addLoaiYeuTo(data);
      } else if (formMode === 'edit' && selectedObj) {
        await updateLoaiYeuTo(selectedObj.uuid, data);
      }

      handleCloseForm();
      clearSelection();
      await refreshLoaiYeuTos();
    } catch (error: any) {
      let errorMessage = 'Có lỗi xảy ra, vui lòng thử lại sau.';

      if (error.response?.data?.error?.includes('already exists')) {
        errorMessage = `Mã loại yếu tố "${data.ma_loai}" đã tồn tại trong hệ thống.`;
      } else if (error.response?.data?.detail) {
        errorMessage = error.response.data.detail;
      } else if (error.message) {
        errorMessage = error.message;
      }

      toast({
        title: 'Lỗi',
        description: errorMessage,
        variant: 'destructive'
      });
    }
  };

  const tables = [
    {
      name: '',
      rows: loaiYeuTos,
      columns: factorTypeColumns
    }
  ];

  return (
    <div className='flex h-full min-h-screen w-screen flex-col lg:overflow-hidden'>
      {showForm && (
        <FormDialog
          open={showForm}
          onSubmit={handleSubmit}
          formMode={formMode}
          initialData={
            selectedObj && (formMode === 'edit' || formMode === 'view' || (formMode === 'add' && isCopyMode))
              ? selectedObj
              : formMode === 'add' && !isCopyMode
                ? undefined
                : undefined
          }
          onClose={handleCloseForm}
          loaiYeuTos={loaiYeuTos}
          onAdd={() => {
            handleCloseForm();
            clearSelection();
            setTimeout(() => {
              handleAddClick();
            }, 100);
          }}
          onEdit={() => {
            if (selectedObj) {
              handleCloseForm();
              handleEditClick();
            }
          }}
          onDelete={() => {
            if (selectedObj) {
              handleCloseForm();
              handleDeleteClick();
            }
          }}
          onCopy={() => {
            if (selectedObj) {
              handleCloseForm();
              handleCopyClick();
            }
          }}
        />
      )}

      {showDelete && (
        <ConfirmDialog
          open={showDelete}
          onClose={handleCloseDelete}
          selectedObj={selectedObj}
          onDelete={deleteLoaiYeuTo}
          clearSelection={clearSelection}
          title='Xoá dữ liệu'
          content='Bạn có chắc chắn muốn xoá không?'
        />
      )}

      <>
        <ActionBar
          onAddClick={handleAddClick}
          onRefreshClick={refreshLoaiYeuTos}
          onExportClick={() => {}}
          onEditClick={() => selectedObj && handleEditClick()}
          onViewClick={() => selectedObj && handleViewClick()}
          onCopyClick={() => selectedObj && handleCopyClick()}
          onDeleteClick={() => selectedObj && handleDeleteClick()}
          isViewDisabled={!selectedObj}
        />

        {isLoading && <LoadingOverlay />}
        {!isLoading && (
          <div className='w-full overflow-hidden'>
            <AritoDataTables
              tables={tables}
              selectedRowId={selectedRowIndex || undefined}
              onRowClick={handleRowClick}
            />
          </div>
        )}
      </>
    </div>
  );
}
