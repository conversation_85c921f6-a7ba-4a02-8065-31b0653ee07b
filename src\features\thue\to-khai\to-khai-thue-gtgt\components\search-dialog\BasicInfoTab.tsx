import React from 'react';
import { RadioButton } from '@/components/custom/arito/form/radio-button';
import { FormField } from '@/components/custom/arito/form/form-field';
import { Label } from '@/components/ui/label';

interface BasicInfoTabProps {
  formMode: 'add' | 'edit' | 'view';
}

const BasicInfoTab: React.FC<BasicInfoTabProps> = ({ formMode }) => {
  return (
    <div className='space-y-2 p-4'>
      <div className='space-y-2'>
        {/* Thời gian tờ khai */}
        <div className='flex items-center'>
          <Label className='w-32 min-w-32 text-sm font-medium'>Thời gian tờ khai</Label>
          <FormField
            name='thoi_gian_to_khai'
            type='select'
            className='w-72'
            options={[
              { value: 'to_khai_thang', label: 'Tờ khai tháng' },
              { value: 'to_khai_quy', label: 'Tờ khai quý' },
              { value: 'to_khai_nam', label: 'Tờ khai năm' }
            ]}
            disabled={formMode === 'view'}
          />
        </div>

        {/* Tháng */}
        <div className='flex items-center'>
          <Label className='w-32 min-w-32 text-sm font-medium'>Tháng</Label>
          <FormField name='thang' type='number' className='w-20' disabled={formMode === 'view'} />
        </div>

        {/* Năm */}
        <div className='flex items-center'>
          <Label className='w-32 min-w-32 text-sm font-medium'>Năm</Label>
          <FormField name='nam' type='number' className='w-32' disabled={formMode === 'view'} />
        </div>
      </div>
    </div>
  );
};

export default BasicInfoTab;
