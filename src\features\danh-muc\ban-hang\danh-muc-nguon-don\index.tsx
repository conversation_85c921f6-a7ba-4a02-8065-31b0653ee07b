'use client';

import AritoDataTables from '@/components/custom/arito/data-tables';
import { ActionBar, FormDialog, DeleteDialog } from './components';
import { useNguonDon } from '@/hooks/queries/useNguonDon';
import { getDataTableColumns } from './cols-definition';
import { NguonDonInput } from '@/types/schemas';
import { useFormState } from './hooks';

export default function DanhMucNguonDon() {
  const { nguonDons, isLoading, addNguonDon, updateNguonDon, deleteNguonDon, refreshNguonDons } = useNguonDon();

  const {
    showForm,
    showDelete,
    formMode,
    selectedObj,
    selectedRowIndex,
    isCopyMode,

    handleCloseForm,
    handleCloseDelete,
    handleAddClick,
    handleEditClick,
    handleViewClick,
    handleDeleteClick,
    handleCopyClick,
    handleRowClick,
    clearSelection
  } = useFormState();

  const handleFormSubmit = async (data: NguonDonInput) => {
    try {
      const processedData = {
        ...data,
        status: typeof data.status === 'string' ? Number(data.status) : data.status
      };

      if (formMode === 'add') {
        await addNguonDon(processedData);
      } else if (formMode === 'edit' && selectedObj) {
        await updateNguonDon(selectedObj.uuid, processedData);
      }
      handleCloseForm();
      clearSelection();
    } catch (error) {
      console.error('Error submitting form:', error);
    }
  };

  const processedNguonDons = nguonDons.map(item => ({
    ...item,
    status: Number(item.status)
  }));

  const tables = [
    {
      name: '',
      rows: processedNguonDons,
      columns: getDataTableColumns()
    }
  ];

  return (
    <div className='flex h-screen flex-col lg:overflow-hidden'>
      <div className='w-full'>
        <ActionBar
          onAddClick={handleAddClick}
          onEditClick={() => selectedObj && handleEditClick()}
          onDeleteClick={() => selectedObj && handleDeleteClick()}
          onCopyClick={() => selectedObj && handleCopyClick()}
          onViewClick={() => selectedObj && handleViewClick()}
          onRefreshClick={() => refreshNguonDons()}
        />

        {isLoading && (
          <div className='flex h-64 items-center justify-center'>
            <div className='size-12 animate-spin rounded-full border-b-2 border-t-2 border-blue-500'></div>
          </div>
        )}

        {!isLoading && (
          <AritoDataTables tables={tables} selectedRowId={selectedRowIndex || undefined} onRowClick={handleRowClick} />
        )}
      </div>

      {showDelete && (
        <DeleteDialog
          open={showDelete}
          onClose={handleCloseDelete}
          selectedObj={selectedObj}
          deleteNguonDon={deleteNguonDon}
          clearSelection={clearSelection}
        />
      )}

      {showForm && (
        <FormDialog
          open={showForm}
          onClose={handleCloseForm}
          onSubmit={handleFormSubmit}
          formMode={formMode}
          initialData={
            selectedObj && (formMode === 'edit' || formMode === 'view' || (formMode === 'add' && isCopyMode))
              ? selectedObj
              : undefined
          }
          existingCodes={nguonDons.map(item => item.ma_nguondon)}
          onAddButtonClick={() => {
            handleCloseForm();
            clearSelection();
            setTimeout(() => {
              handleAddClick();
            }, 100);
          }}
          onEditButtonClick={() => {
            if (selectedObj) {
              handleCloseForm();
              handleEditClick();
            }
          }}
          onDeleteButtonClick={() => {
            if (selectedObj) {
              handleCloseForm();
              handleDeleteClick();
            }
          }}
          onCopyButtonClick={() => {
            if (selectedObj) {
              handleCloseForm();
              handleCopyClick();
            }
          }}
        />
      )}
    </div>
  );
}
