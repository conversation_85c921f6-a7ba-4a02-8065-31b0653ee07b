import { <PERSON>lip<PERSON><PERSON><PERSON><PERSON><PERSON>, Pin, Printer, Refresh<PERSON><PERSON>, Save, Search, Settings, Sheet } from 'lucide-react';
import React from 'react';
import AritoMenuButton from '@/components/custom/arito/menu-button';
import AritoActionBar from '@/components/custom/arito/action-bar';
import { AritoActionButton } from '@/components/custom/arito';
import AritoIcon from '@/components/custom/arito/icon';
import { TableConfig } from '../hooks/useTableData';
import { SearchFormValues } from '../schema';

interface ActionBarProps {
  currentTable: TableConfig;
  onSearchClick: () => void;
  onRefreshClick: () => void;
  onLapToKhaiClick?: () => void;
  onCoDinhCotClick?: () => void;
  onChinhSuaMauInClick?: () => void;
  searchParams?: SearchFormValues | null;
}

const ActionBar: React.FC<ActionBarProps> = ({
  currentTable,
  onSearchClick,
  onRefreshClick,
  onLapToKhaiClick,
  onCoDinhCotClick,
  onChinhSuaMauInClick,
  searchParams
}) => {
  const { actionBarConfig, id } = currentTable;

  return (
    <AritoActionBar
      titleComponent={
        <div className='text-lg font-semibold'>
          {currentTable.title}
          {searchParams && (
            <span className='flex items-center justify-start text-xs font-semibold text-gray-500'>
              <div className='mr-2 size-2 rounded-full bg-red-500' />
              Từ tháng {searchParams.thang} đến tháng {searchParams.thang} năm {searchParams.nam}
            </span>
          )}
        </div>
      }
    >
      <AritoActionButton icon={Search} onClick={onSearchClick} title='Tìm kiếm' variant='primary' />

      <AritoMenuButton title='In ấn' icon={Printer} items={[]} />

      {actionBarConfig.showLapToKhai && onLapToKhaiClick && (
        <AritoActionButton icon={Save} onClick={onLapToKhaiClick} title='Lập tờ khai' />
      )}

      {actionBarConfig.showTinhLai && <AritoActionButton icon={ClipboardPenLine} onClick={() => {}} title='Tính lại' />}

      {actionBarConfig.showCoDinhCot && onCoDinhCotClick && (
        <AritoActionButton icon={Pin} onClick={onCoDinhCotClick} title='Cố định cột' />
      )}

      {actionBarConfig.showChinhSuaMauIn && onChinhSuaMauInClick && (
        <AritoActionButton icon={Settings} onClick={onChinhSuaMauInClick} title='Chỉnh sửa mẫu in' />
      )}

      <AritoActionButton icon={RefreshCw} onClick={onRefreshClick} title='Refresh' />

      {actionBarConfig.showKetXuatDuLieu && (
        <AritoActionButton icon={Sheet} onClick={() => {}} title='Kết xuất dữ liệu' />
      )}

      {id === 'to-khai-gtgt' && (
        <AritoMenuButton
          title='Khác'
          items={[
            {
              title: 'Cố định cột',
              icon: <AritoIcon icon={16} />,
              onClick: onCoDinhCotClick || (() => {}),
              group: 0
            },
            {
              title: 'Tải file XML HTKK',
              icon: <AritoIcon icon={688} />,
              onClick: () => {},
              group: 1
            },
            {
              title: 'Nộp tờ khai trực tiếp lên thuế',
              icon: <AritoIcon icon={684} />,
              onClick: () => {},
              group: 1
            },
            {
              title: 'Chỉnh sửa mẫu in',
              icon: <AritoIcon icon={864} />,
              onClick: onChinhSuaMauInClick || (() => {}),
              group: 2
            },
            {
              title: 'Kết xuất dữ liệu',
              icon: <AritoIcon icon={555} />,
              onClick: () => {},
              group: 2
            }
          ]}
        />
      )}

      {(id === 'to-khai-gtgt-pl43' || id === 'pl-giam-thue-gtgt' || id === 'to-khai-gtgt-pl142') && (
        <AritoMenuButton
          title='Khác'
          items={[
            {
              title: 'Kết xuất dữ liệu',
              icon: <AritoIcon icon={555} />,
              onClick: () => {},
              group: 0
            }
          ]}
        />
      )}

      {(id === 'bang-ke-ban-ra' || id === 'bang-ke-mua-vao') && (
        <AritoMenuButton
          title='Khác'
          items={[
            {
              title: 'Chỉnh sửa mẫu in',
              icon: <AritoIcon icon={864} />,
              onClick: onChinhSuaMauInClick || (() => {}),
              group: 0
            }
          ]}
        />
      )}
    </AritoActionBar>
  );
};

export default ActionBar;
