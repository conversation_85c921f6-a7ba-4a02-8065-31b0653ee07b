'use client';

import { useState } from 'react';
import { useDialogState, useTableData, useActionHandlers, useVatTuXuatSanXuatNhungChuaKhaiBaoDinhMuc } from './hooks';
import { AritoDataTables, LoadingOverlay } from '@/components/custom/arito';
import { InitialSearchDialog, ActionBar } from './components';
import { DonViChiNhanh } from '@/types/schemas';

export default function VatTuXuatSanXuatNhungChuaKhaiBaoDinhMucPage() {
  const {
    initialSearchDialogOpen,
    showTable,

    handleInitialSearchClose,
    handleInitialSearch,
    handleSearchClick,
    handleEditPrintTemplateClick
  } = useDialogState();

  const { handleRefreshClick, handleFixedColumnsClick, handleExportDataClick } = useActionHandlers();

  const [subTitle, setSubTitle] = useState<string>('');
  const [donViChiNhanh, setDonViChiNhanh] = useState<DonViChiNhanh | null>(null);
  const [ky, setKy] = useState<number>(0);
  const [nam, setNam] = useState<number>(0);
  const { data, isLoading, fetchData, refreshData } = useVatTuXuatSanXuatNhungChuaKhaiBaoDinhMuc({
    donViChiNhanh: donViChiNhanh?.uuid,
    ky,
    nam
  });
  const { tables, handleRowClick } = useTableData(data);

  const handleSearchWithData = async (values: any) => {
    const searchParams = {
      donViChiNhanh: donViChiNhanh?.uuid,
      ky,
      nam,
      ...values
    };

    await handleInitialSearch(searchParams, fetchData);
  };

  const handleRefreshWithData = async () => {
    await refreshData();
    handleRefreshClick();
  };

  return (
    <div className='flex h-full min-h-[calc(100vh-64px)] w-screen flex-col overflow-auto'>
      <InitialSearchDialog
        open={initialSearchDialogOpen}
        onClose={handleInitialSearchClose}
        onSearch={handleSearchWithData}
        onSetSubTitle={setSubTitle}
        onSetDonViChiNhanh={setDonViChiNhanh}
        onSetKy={setKy}
        onSetNam={setNam}
        donViChiNhanh={donViChiNhanh}
        ky={ky}
        nam={nam}
      />

      {showTable && (
        <>
          <ActionBar
            onSearchClick={handleSearchClick}
            onRefreshClick={handleRefreshWithData}
            onFixedColumnsClick={handleFixedColumnsClick}
            onExportDataClick={handleExportDataClick}
            onEditPrintTemplateClick={handleEditPrintTemplateClick}
            className='border-b border-gray-200'
            subTitle={subTitle}
          />

          <div className='flex-1 overflow-hidden'>
            {isLoading && <LoadingOverlay />}
            {!isLoading && <AritoDataTables tables={tables} onRowClick={handleRowClick} />}
          </div>
        </>
      )}
    </div>
  );
}
