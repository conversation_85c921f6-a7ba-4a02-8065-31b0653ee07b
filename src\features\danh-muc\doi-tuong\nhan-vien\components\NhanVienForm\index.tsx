import HeaderBar from '@/components/custom/arito/form/header-bar';
import { nhanVienSchema, initialValues } from '../../schemas';
import { AritoForm } from '@/components/custom/arito/form';
import { <PERSON>han<PERSON>ien, NhanVienInput } from '@/types/schemas';
import { useSearchFieldStates } from '../../hooks';
import BasicInfoTab from './BasicInfoTab';

interface NhanVienFormProps {
  open: boolean;
  onClose: () => void;
  onSubmit: (data: NhanVienInput) => void;
  formMode: 'add' | 'edit' | 'view';
  initialData?: NhanVien;
  onAddButtonClick?: () => void;
  onEditButtonClick?: () => void;
  onDeleteButtonClick?: () => void;
  onCopyButtonClick?: () => void;
}

export const NhanVienForm: React.FC<NhanVienFormProps> = ({
  open,
  onClose,
  onSubmit,
  formMode,
  initialData,
  onAddButtonClick,
  onEditButtonClick,
  onDeleteButtonClick,
  onCopyButtonClick
}) => {
  const { department, setDepartment, location, setLocation } = useSearchFieldStates(initialData);

  const handleSubmit = async (data: any) => {
    try {
      const formattedData: NhanVienInput = {
        ...data,
        ma_bo_phan: department?.uuid,
        noi_cap_cmnd: location?.uuid
      };

      onSubmit(formattedData);
    } catch (error) {
      console.error('Error submitting form:', error);
    }
  };

  return (
    <AritoForm
      mode={formMode}
      title={formMode === 'add' ? 'Mới' : undefined}
      onClose={onClose}
      onSubmit={handleSubmit}
      initialData={initialData || initialValues}
      schema={nhanVienSchema}
      hasAritoActionBar={false}
      headerBar={
        <HeaderBar
          onClose={onClose}
          mode={formMode}
          title='Mới'
          subTitle='Danh mục nhân viên'
          onAdd={onAddButtonClick}
          onEdit={onEditButtonClick}
          onDelete={onDeleteButtonClick}
          onCopy={onCopyButtonClick}
        />
      }
      headerFields={
        <BasicInfoTab
          formMode={formMode}
          department={department}
          location={location}
          setDepartment={setDepartment}
          setLocation={setLocation}
        />
      }
    />
  );
};

export default NhanVienForm;
