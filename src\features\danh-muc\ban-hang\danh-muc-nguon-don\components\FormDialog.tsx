import { But<PERSON> } from '@mui/material';
import { useState } from 'react';
import { BottomBar } from '@/components/custom/arito/form/bottom-bar';
import { AritoDialog } from '@/components/custom/arito/dialog';
import { AritoForm } from '@/components/custom/arito/form';
import { NguonDonInput, NguonDon } from '@/types/schemas';
import AritoIcon from '@/components/custom/arito/icon';
import { formSchema } from '../schemas';
import BasicInfo from './BasicInfo';

interface FormDialogProps {
  open: boolean;
  onClose: () => void;
  onSubmit: (data: NguonDonInput) => void;
  formMode: 'add' | 'edit' | 'view';
  initialData?: NguonDon;
  onAddButtonClick?: () => void;
  onEditButtonClick?: () => void;
  onDeleteButtonClick?: () => void;
  onCopyButtonClick?: () => void;
  existingCodes?: string[];
}

const FormDialog = ({
  open,
  onClose,
  onSubmit,
  formMode,
  initialData,
  onAddButtonClick,
  onEditButtonClick,
  onDeleteButtonClick,
  onCopyButtonClick,
  existingCodes = []
}: FormDialogProps) => {
  const [error, setError] = useState<string | null>(null);

  const handleSubmit = async (data: any) => {
    try {
      if (!data.ma_nguondon) {
        setError('Mã nguồn đơn là bắt buộc');
        return;
      }

      if (!data.ten_nguondon) {
        setError('Tên nguồn đơn là bắt buộc');
        return;
      }

      if (formMode === 'add' && existingCodes.includes(data.ma_nguondon)) {
        setError('Mã nguồn đơn đã tồn tại trong danh sách');
        return;
      }

      const updatedData: NguonDonInput = {
        ma_nguondon: data.ma_nguondon,
        ten_nguondon: data.ten_nguondon,
        ghi_chu: data.ghi_chu || null,
        status: data.status
      };
      onSubmit(updatedData);
    } catch (err: any) {
      setError(err.message || 'Có lỗi xảy ra');
    }
  };

  const title = formMode === 'add' ? 'Mới' : formMode === 'edit' ? 'Sửa' : 'Xem';

  return (
    <AritoDialog
      open={open}
      onClose={onClose}
      title={title}
      titleIcon={<AritoIcon icon={281} />}
      maxWidth='md'
      disableBackdropClose={true}
      disableEscapeKeyDown={true}
    >
      <AritoForm
        mode={formMode}
        hasAritoActionBar={false}
        onSubmit={handleSubmit}
        schema={formSchema}
        initialData={
          formMode === 'add' && initialData
            ? {
                ...initialData,
                uuid: undefined,
                ma_nguondon: '',
                status: Number(initialData.status)
              }
            : initialData
              ? {
                  ...initialData,
                  status: Number(initialData.status)
                }
              : undefined
        }
        className='w-full md:min-w-[500px] lg:min-w-[600px]'
        headerFields={<BasicInfo formMode={formMode} />}
        classNameBottomBar='relative w-full flex justify-end gap-2'
        bottomBar={
          <>
            {formMode === 'view' && (
              <BottomBar
                mode={formMode}
                onAdd={onAddButtonClick}
                onEdit={onEditButtonClick}
                onDelete={onDeleteButtonClick}
                onCopy={onCopyButtonClick}
                onSubmit={() => {}}
                onClose={onClose}
              />
            )}

            {formMode !== 'view' && (
              <div className='space-x-2 p-2'>
                <Button
                  className='bg-[rgba(15,118,110,0.9)] text-white hover:bg-[rgba(15,118,110,1)]'
                  type='submit'
                  variant='contained'
                >
                  <AritoIcon icon={884} marginX='4px' />
                  Đồng ý
                </Button>

                <Button onClick={() => onClose()} variant='outlined'>
                  <AritoIcon icon={885} marginX='4px' />
                  Huỷ
                </Button>
              </div>
            )}
          </>
        }
      />
      {error && <div className='mx-4 mb-4 rounded bg-red-100 p-2 text-red-700'>{error}</div>}
    </AritoDialog>
  );
};

export default FormDialog;
