import { useState, useEffect, useRef } from 'react';
import { GroupType } from '@/types/schemas';

export const useDataRefresh = (
  refreshFn: (groupType?: GroupType | null | any) => Promise<void>,
  activeGroup: GroupType,
  autoRefreshInterval: number = 0
) => {
  const [isRefreshing, setIsRefreshing] = useState<boolean>(false);
  const [lastRefreshed, setLastRefreshed] = useState<Date | null>(null);
  const [isAutoRefreshEnabled, setIsAutoRefreshEnabled] = useState<boolean>(autoRefreshInterval > 0);
  const refreshIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const activeGroupRef = useRef(activeGroup);
  const refreshFnRef = useRef(refreshFn);

  useEffect(() => {
    activeGroupRef.current = activeGroup;
    refreshFnRef.current = refreshFn;
  }, [activeGroup, refreshFn]);

  const refreshData = async () => {
    setIsRefreshing(true);

    try {
      await refreshFnRef.current(activeGroupRef.current);
      setLastRefreshed(new Date());
    } catch (error) {
      console.error('Error refreshing data:', error);
    } finally {
      setIsRefreshing(false);
    }
  };

  const toggleAutoRefresh = () => {
    setIsAutoRefreshEnabled(prev => !prev);
  };

  useEffect(() => {
    if (refreshIntervalRef.current) {
      clearInterval(refreshIntervalRef.current);
      refreshIntervalRef.current = null;
    }

    if (isAutoRefreshEnabled && autoRefreshInterval > 0) {
      refreshIntervalRef.current = setInterval(refreshData, autoRefreshInterval);
    }

    return () => {
      if (refreshIntervalRef.current) {
        clearInterval(refreshIntervalRef.current);
      }
    };
  }, [isAutoRefreshEnabled, autoRefreshInterval]);

  useEffect(() => {
    const handleFocus = () => {
      if (lastRefreshed && new Date().getTime() - lastRefreshed.getTime() > 5 * 60 * 1000) {
        refreshData();
      }
    };

    window.addEventListener('focus', handleFocus);

    return () => {
      window.removeEventListener('focus', handleFocus);
    };
  }, [lastRefreshed]);

  useEffect(() => {
    refreshData();
  }, [activeGroup]);

  return {
    isRefreshing,
    lastRefreshed,
    isAutoRefreshEnabled,
    refreshData,
    toggleAutoRefresh
  };
};
