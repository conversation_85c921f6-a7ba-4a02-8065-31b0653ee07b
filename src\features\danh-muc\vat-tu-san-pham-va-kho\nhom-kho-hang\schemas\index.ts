import { z } from 'zod';

export const warehouseGroupSchema = z.object({
  ma_nhom: z
    .string()
    .min(1, 'Mã nhóm không được để trống')
    .max(50, 'Mã nhóm không được vượt quá 50 ký tự')
    .regex(/^[a-zA-Z0-9_-]+$/, 'Mã nhóm chỉ được chứa chữ cái, số, gạch ngang và gạch dưới'),
  ten_phan_nhom: z.string().min(1, 'Tên nhóm không được để trống').max(255, 'Tên nhóm không được vượt quá 255 ký tự'),
  ten2: z.string().max(255, 'Tên 2 không được vượt quá 255 ký tự').nullable().optional(),
  trang_thai: z.enum(['0', '1'], {
    errorMap: () => ({ message: 'Trạng thái không hợp lệ' })
  }),
  loai_nhom: z.string()
});

export const initialFormValues = {
  ma_nhom: '',
  ten_phan_nhom: '',
  ten2: '',
  trang_thai: '1',
  loai_nhom: ''
};
