import { But<PERSON> } from '@mui/material';
import React from 'react';
import { SearchFormValues, searchSchema, searchInitData } from '../../schema';
import { AritoHeaderTabs } from '@/components/custom/arito/header-tabs';
import { AritoDialog } from '@/components/custom/arito/dialog';
import { AritoForm } from '@/components/custom/arito/form';
import AritoIcon from '@/components/custom/arito/icon';
import BasicInfoTab from './BasicInfoTab';
import DetailTab from './DetailTab';

interface SearchDialogProps {
  openSearchDialog: boolean;
  onCloseSearchDialog: () => void;
  onSearch: (data: SearchFormValues) => void;
  updateTableColumns?: (templateValue: string) => void;
}

const SearchDialog: React.FC<SearchDialogProps> = ({
  openSearchDialog,
  onCloseSearchDialog,
  onSearch,
  updateTableColumns
}) => {
  const handleSubmit = (data: SearchFormValues) => {
    onSearch(data);
    if (updateTableColumns && data.mau_bao_cao) {
      updateTableColumns(data.mau_bao_cao);
    }

    onCloseSearchDialog();
  };

  return (
    <AritoDialog
      open={openSearchDialog}
      onClose={onCloseSearchDialog}
      title='Tờ khai thuế giá trị gia tăng'
      maxWidth='xl'
      disableBackdropClose={true}
      disableEscapeKeyDown={true}
      titleIcon={<AritoIcon icon={12} />}
    >
      <AritoForm
        mode='add'
        hasAritoActionBar={false}
        schema={searchSchema}
        onSubmit={handleSubmit}
        initialData={searchInitData}
        className='w-[900px]'
        headerFields={
          <div className='max-h-[calc(100vh-150px)] overflow-y-auto'>
            <BasicInfoTab formMode='add' />

            <AritoHeaderTabs
              tabs={[
                {
                  id: 'chi_tiet',
                  label: 'Chi tiết',
                  component: <DetailTab formMode='add' />
                }
              ]}
            />
          </div>
        }
        classNameBottomBar='relative w-full flex justify-end gap-2 px-2 py-1'
        bottomBar={
          <>
            <Button
              className='bg-[rgba(15,118,110,0.9)] text-white hover:bg-[rgba(15,118,110,1)]'
              type='submit'
              variant='contained'
            >
              <AritoIcon icon={884} marginX='4px' />
              Đồng ý
            </Button>

            <Button onClick={onCloseSearchDialog} variant='outlined'>
              <AritoIcon icon={885} marginX='4px' />
              Huỷ
            </Button>
          </>
        }
      />
    </AritoDialog>
  );
};

export default SearchDialog;
