import { GridColDef, GridRenderCellParams } from '@mui/x-data-grid';
import { NguonDon } from '@/types/schemas';

export function getDataTableColumns(): GridColDef<NguonDon>[] {
  return [
    {
      field: 'ma_nguondon',
      headerName: 'Mã nguồn đơn',
      width: 150
    },
    {
      field: 'ten_nguondon',
      headerName: 'Tên nguồn đơn',
      width: 250
    },
    {
      field: 'ghi_chu',
      headerName: 'Ghi chú',
      width: 200
    },
    {
      field: 'status',
      headerName: 'Trạng thái',
      width: 150,
      renderCell: (params: GridRenderCellParams<NguonDon>) => {
        const row = params.row;

        return row.status === 1 ? '1. Còn sử dụng' : '0. Không sử dụng';
      }
    }
  ];
}
