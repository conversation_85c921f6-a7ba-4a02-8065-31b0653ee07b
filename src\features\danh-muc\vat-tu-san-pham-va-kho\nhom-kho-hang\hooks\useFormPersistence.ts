import { useState, useEffect, useRef } from 'react';
import { GroupType } from '@/types/schemas';

const FORM_DRAFT_KEY = 'warehouse-group-form-draft';

export const useFormPersistence = (activeGroup: GroupType) => {
  const [hasDraft, setHasDraft] = useState<boolean>(false);
  const activeGroupRef = useRef<GroupType>(activeGroup);

  useEffect(() => {
    activeGroupRef.current = activeGroup;
  }, [activeGroup]);

  const saveDraft = (formData: any) => {
    try {
      const draft = {
        formData,
        groupType: activeGroupRef.current,
        timestamp: new Date().toISOString()
      };

      localStorage.setItem(FORM_DRAFT_KEY, JSON.stringify(draft));

      setHasDraft(true);
    } catch (error) {
      console.error('Error saving form draft:', error);
    }
  };

  const loadDraft = (): { formData: any; groupType: GroupType; timestamp: string } | null => {
    try {
      const draftJson = localStorage.getItem(FORM_DRAFT_KEY);

      if (!draftJson) {
        return null;
      }

      const draft = JSON.parse(draftJson);

      if (draft.groupType !== activeGroupRef.current) {
        return null;
      }

      return draft;
    } catch (error) {
      console.error('Error loading form draft:', error);
      return null;
    }
  };

  const clearDraft = () => {
    try {
      localStorage.removeItem(FORM_DRAFT_KEY);

      setHasDraft(false);
    } catch (error) {
      console.error('Error clearing form draft:', error);
    }
  };

  useEffect(() => {
    const draft = loadDraft();
    setHasDraft(!!draft);
  }, [activeGroup]);

  return {
    hasDraft,
    saveDraft,
    loadDraft,
    clearDraft
  };
};
