import { useState, useEffect, useCallback } from 'react';
import { ChungTu, ChungTuInput, ChungTuListResponse, ChungTuSearchParams } from '@/types/schemas/chung-tu.type';
import { useAuth } from '@/contexts/auth-context';
import api from '@/lib/api';

interface UseChungTuReturn {
  chungTus: ChungTu[];
  isLoading: boolean;
  addChungTu: (newChungTu: ChungTuInput) => Promise<ChungTu>;
  updateChungTu: (uuid: string, updatedChungTu: ChungTuInput) => Promise<ChungTu>;
  deleteChungTu: (uuid: string) => Promise<void>;
  refreshChungTus: () => Promise<void>;
  searchChungTus: (searchParams: ChungTuSearchParams) => Promise<void>;
  getChungTuByCode: (ma_ct: string) => Promise<ChungTu | null>;
  getChungTuByType: (loai_chung_tu: string) => Promise<ChungTu[]>;
  getChungTuByStatus: (trang_thai: string) => Promise<ChungTu[]>;
  getActiveChungTus: () => Promise<ChungTu[]>;
  getChungTuById: (uuid: string) => Promise<ChungTu | null>;
}

/**
 * Hook for managing ChungTu (Document/Voucher) data
 *
 * This hook provides functions to fetch, create, update, and delete documents/vouchers,
 * as well as search and filter functionality for ChungTu entities.
 */
export const useChungTu = (initialChungTus: ChungTu[] = []): UseChungTuReturn => {
  const [chungTus, setChungTus] = useState<ChungTu[]>(initialChungTus);
  const [isLoading, setIsLoading] = useState<boolean>(true);

  const { entity } = useAuth();

  const fetchChungTus = useCallback(async () => {
    if (!entity?.slug) return;

    setIsLoading(true);
    try {
      const response = await api.get<ChungTuListResponse>(`/entities/${entity.slug}/erp/documents/`);
      setChungTus(response.data.results);
    } catch (error) {
      console.error('Error fetching documents:', error);
    } finally {
      setIsLoading(false);
    }
  }, [entity?.slug]);

  const addChungTu = async (newChungTu: ChungTuInput): Promise<ChungTu> => {
    if (!entity?.slug) throw new Error('Entity slug is required');

    setIsLoading(true);
    try {
      const response = await api.post<ChungTu>(`/entities/${entity.slug}/erp/documents/`, newChungTu);
      const createdChungTu = response.data;
      setChungTus(prev => [...prev, createdChungTu]);
      return createdChungTu;
    } catch (error) {
      console.error('Error creating document:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const updateChungTu = async (uuid: string, updatedChungTu: ChungTuInput): Promise<ChungTu> => {
    if (!entity?.slug) throw new Error('Entity slug is required');

    setIsLoading(true);
    try {
      const response = await api.put<ChungTu>(`/entities/${entity.slug}/erp/documents/${uuid}/`, updatedChungTu);
      const updatedData = response.data;
      setChungTus(prev => prev.map(item => (item.uuid === uuid ? updatedData : item)));
      return updatedData;
    } catch (error) {
      console.error('Error updating document:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const deleteChungTu = async (uuid: string): Promise<void> => {
    if (!entity?.slug) return;

    setIsLoading(true);
    try {
      await api.delete(`/entities/${entity.slug}/erp/documents/${uuid}/`);
      setChungTus(prev => prev.filter(item => item.uuid !== uuid));
    } catch (error) {
      console.error('Error deleting document:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const searchChungTus = async (searchParams: ChungTuSearchParams): Promise<void> => {
    if (!entity?.slug) return;

    setIsLoading(true);
    try {
      const queryParams = new URLSearchParams();

      // Add search parameters to query string
      if (searchParams.ma_ct) queryParams.append('ma_ct', searchParams.ma_ct);
      if (searchParams.ten_ct) queryParams.append('ten_ct', searchParams.ten_ct);
      if (searchParams.loai_chung_tu) queryParams.append('loai_chung_tu', searchParams.loai_chung_tu);
      if (searchParams.trang_thai_ngam_dinh)
        queryParams.append('trang_thai_ngam_dinh', searchParams.trang_thai_ngam_dinh);
      if (searchParams.page) queryParams.append('page', searchParams.page.toString());
      if (searchParams.page_size) queryParams.append('page_size', searchParams.page_size.toString());

      const response = await api.get<ChungTuListResponse>(
        `/entities/${entity.slug}/erp/documents/?${queryParams.toString()}`
      );
      setChungTus(response.data.results);
    } catch (error) {
      console.error('Error searching documents:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const getChungTuByCode = async (ma_ct: string): Promise<ChungTu | null> => {
    if (!entity?.slug) return null;

    try {
      const response = await api.get<ChungTuListResponse>(
        `/entities/${entity.slug}/erp/documents/?ma_ct=${encodeURIComponent(ma_ct)}`
      );
      return response.data.results.length > 0 ? response.data.results[0] : null;
    } catch (error) {
      console.error('Error fetching document by code:', error);
      return null;
    }
  };

  const getChungTuByType = async (loai_chung_tu: string): Promise<ChungTu[]> => {
    if (!entity?.slug) return [];

    try {
      const response = await api.get<ChungTuListResponse>(
        `/entities/${entity.slug}/erp/documents/?loai_chung_tu=${encodeURIComponent(loai_chung_tu)}`
      );
      return response.data.results;
    } catch (error) {
      console.error('Error fetching documents by type:', error);
      return [];
    }
  };

  const getChungTuByStatus = async (trang_thai: string): Promise<ChungTu[]> => {
    if (!entity?.slug) return [];

    try {
      const response = await api.get<ChungTuListResponse>(
        `/entities/${entity.slug}/erp/documents/?trang_thai_ngam_dinh=${encodeURIComponent(trang_thai)}`
      );
      return response.data.results;
    } catch (error) {
      console.error('Error fetching documents by status:', error);
      return [];
    }
  };

  const getActiveChungTus = async (): Promise<ChungTu[]> => {
    if (!entity?.slug) return [];

    try {
      // Since there's no specific active endpoint, we'll filter by active status
      // This assumes there's an 'active' field or similar in the model
      const response = await api.get<ChungTuListResponse>(`/entities/${entity.slug}/erp/documents/?active=true`);
      return response.data.results;
    } catch (error) {
      console.error('Error fetching active documents:', error);
      return [];
    }
  };

  const getChungTuById = async (uuid: string): Promise<ChungTu | null> => {
    if (!entity?.slug) return null;

    try {
      const response = await api.get<ChungTu>(`/entities/${entity.slug}/erp/documents/${uuid}/`);
      return response.data;
    } catch (error) {
      console.error('Error fetching document by ID:', error);
      return null;
    }
  };

  useEffect(() => {
    fetchChungTus();
  }, [fetchChungTus]);

  return {
    chungTus,
    isLoading,
    addChungTu,
    updateChungTu,
    deleteChungTu,
    refreshChungTus: fetchChungTus,
    searchChungTus,
    getChungTuByCode,
    getChungTuByType,
    getChungTuByStatus,
    getActiveChungTus,
    getChungTuById
  };
};
