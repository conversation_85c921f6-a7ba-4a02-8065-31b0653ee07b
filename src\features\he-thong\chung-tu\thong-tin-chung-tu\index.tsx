'use client';

import React from 'react';
import { FormDialog, ActionBar, Sidebar, AddFieldDialog } from './components';
import { LoadingOverlay, AritoDataTables } from '@/components/custom/arito';
import { DialogProvider } from './contexts/DialogContext';
import { DocumentsColumns } from './cols-definition';
import { useFormState, useRows } from '@/hooks';
import { useChungTu } from '@/hooks/queries';
import { useGroupFilter } from './hooks';

export default function ThongTinChungTu() {
  const { chungTus, isLoading } = useChungTu();

  const { selectedObj, selectedRowIndex, handleRowClick, clearSelection } = useRows();
  const {
    showForm,
    showDelete,
    formMode,
    isCopyMode,

    handleCloseForm,
    handleCloseDelete,
    handleAddClick,
    handleEditClick,
    handleViewClick,
    handleDeleteClick,
    handleCopyClick
  } = useFormState();

  const { activeGroup, sidebarO<PERSON>, Group, handleFilter, toggleSidebar } = useGroupFilter();

  const tables = [
    {
      name: '',
      rows: [],
      columns: DocumentsColumns(handleViewClick)
    }
  ];

  return (
    <DialogProvider>
      <div className='flex h-screen flex-col lg:overflow-hidden'>
        {showForm && formMode !== 'add' && (
          <FormDialog
            open={showForm}
            formMode={formMode}
            initialData={selectedObj}
            onClose={handleCloseForm}
            onSubmit={data => console.log('submit', data)}
          />
        )}

        {showForm && formMode === 'add' && (
          <AddFieldDialog open={showForm} onClose={handleCloseForm} formMode={formMode} initialData={selectedObj} />
        )}

        {(!showForm || formMode === 'add') && (
          <div className='flex h-full'>
            <Sidebar
              activeGroup={activeGroup}
              onFilterChange={handleFilter}
              group={Group}
              isOpen={sidebarOpen}
              toggleSidebar={toggleSidebar}
            />

            <div className='flex h-full flex-1 flex-col overflow-hidden'>
              <ActionBar
                onEditClick={() => selectedObj && handleEditClick()}
                onRefreshClick={() => console.log('Refresh')}
                onFixedColumnsClick={() => console.log('Fixed Columns')}
                onAddFieldClick={() => selectedObj && handleAddClick()}
              />

              <div className='w-full overflow-hidden'>
                {isLoading && (
                  <div className='flex h-full items-center justify-center'>
                    <LoadingOverlay />
                  </div>
                )}

                {!isLoading && (
                  <AritoDataTables
                    tables={tables}
                    selectedRowId={selectedRowIndex || undefined}
                    onRowClick={handleRowClick}
                  />
                )}
              </div>
            </div>
          </div>
        )}
      </div>
    </DialogProvider>
  );
}
