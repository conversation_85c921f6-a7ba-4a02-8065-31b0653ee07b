import { useState, useEffect } from 'react';
import { NguonDon, NguonDonResponse, NguonDonInput } from '@/types/schemas/nguon-don.type';
import { useAuth } from '@/contexts/auth-context';
import QUERY_KEYS from '@/constants/query-keys';
import api from '@/lib/api';

interface UseNguonDonReturn {
  nguonDons: NguonDon[];
  isLoading: boolean;
  addNguonDon: (data: NguonDonInput) => Promise<NguonDon>;
  updateNguonDon: (uuid: string, data: NguonDonInput) => Promise<NguonDon>;
  deleteNguonDon: (uuid: string) => Promise<void>;
  refreshNguonDons: () => Promise<void>;
}

export const useNguonDon = (initialNguonDons: NguonDon[] = []): UseNguonDonReturn => {
  const [nguonDons, setNguonDons] = useState<NguonDon[]>(initialNguonDons);
  const [isLoading, setIsLoading] = useState<boolean>(true);

  const { entity } = useAuth();

  const fetchNguonDons = async () => {
    if (!entity?.slug) return;

    setIsLoading(true);
    try {
      const response = await api.get<NguonDonResponse>(
        `/entities/${entity.slug}/erp/${QUERY_KEYS.DANH_MUC_NGUON_DON}/`
      );
      const mappedData: NguonDon[] = response.data.results;

      const processedData = mappedData.map(item => ({
        ...item,
        status: Number(item.status)
      }));

      setNguonDons(processedData);
    } catch (error) {
      console.error('Error fetching nguon don:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const addNguonDon = async (data: NguonDonInput): Promise<NguonDon> => {
    if (!entity?.slug) throw new Error('Entity slug is required');

    if (!data.ma_nguondon) {
      throw new Error('Mã nguồn đơn là bắt buộc');
    }

    if (!data.ten_nguondon) {
      throw new Error('Tên nguồn đơn là bắt buộc');
    }

    const existingCode = nguonDons.find(item => item.ma_nguondon === data.ma_nguondon);
    if (existingCode) {
      throw new Error('Mã nguồn đơn đã tồn tại trong danh sách');
    }

    setIsLoading(true);
    try {
      const response = await api.post(`/entities/${entity.slug}/erp/order-sources/`, {
        ma_nguondon: data.ma_nguondon,
        ten_nguondon: data.ten_nguondon,
        ghi_chu: data.ghi_chu || null,
        status: typeof data.status === 'string' ? parseInt(data.status, 10) : data.status
      });

      const addedNguonDon: NguonDon = {
        ...response.data,
        status: Number(response.data.status)
      };
      setNguonDons(prev => [...prev, addedNguonDon]);
      return addedNguonDon;
    } catch (error) {
      console.error('Error adding nguon don:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const updateNguonDon = async (uuid: string, data: NguonDonInput): Promise<NguonDon> => {
    if (!entity?.slug) throw new Error('Entity slug is required');

    if (!data.ma_nguondon) {
      throw new Error('Mã nguồn đơn là bắt buộc');
    }

    if (!data.ten_nguondon) {
      throw new Error('Tên nguồn đơn là bắt buộc');
    }

    setIsLoading(true);
    try {
      const response = await api.put(`/entities/${entity.slug}/erp/order-sources/${uuid}/`, {
        ma_nguondon: data.ma_nguondon,
        ten_nguondon: data.ten_nguondon,
        ghi_chu: data.ghi_chu || null,
        status: typeof data.status === 'string' ? parseInt(data.status, 10) : data.status
      });

      const updatedNguonDon: NguonDon = {
        ...response.data,
        status: Number(response.data.status)
      };
      setNguonDons(prev => prev.map(nguonDon => (nguonDon.uuid === updatedNguonDon.uuid ? updatedNguonDon : nguonDon)));
      return updatedNguonDon;
    } catch (error) {
      console.error('Error updating nguon don:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const deleteNguonDon = async (uuid: string): Promise<void> => {
    if (!entity?.slug) throw new Error('Entity slug is required');

    setIsLoading(true);
    try {
      await api.delete(`/entities/${entity.slug}/erp/order-sources/${uuid}/`);
      setNguonDons(prev => prev.filter(nguonDon => nguonDon.uuid !== uuid));
    } catch (error) {
      console.error('Error deleting nguon don:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchNguonDons();
  }, [entity?.slug]);

  return {
    nguonDons,
    isLoading,
    addNguonDon,
    updateNguonDon,
    deleteNguonDon,
    refreshNguonDons: fetchNguonDons
  };
};
