import { <PERSON><PERSON>, Printer, Refresh<PERSON>w, Search, Sheet } from 'lucide-react';
import { AritoActionButton } from '@/components/custom/arito/action-button/index';
import AritoMenuButton from '@/components/custom/arito/menu-button';
import AritoActionBar from '@/components/custom/arito/action-bar';
import AritoIcon from '@/components/custom/arito/icon';

interface ActionBarProps {
  onSearchClick?: () => void;
  onRefreshClick?: () => void;
  onPrintClick?: () => void;
  onFixedColumnsClick?: () => void;
  onExportDataClick?: () => void;
  onEditPrintTemplateClick?: () => void;
  className?: string;
  searchParams?: any;
}

export const ActionBar: React.FC<ActionBarProps> = ({
  onSearchClick,
  onRefreshClick,
  onPrintClick,
  onFixedColumnsClick,
  onExportDataClick,
  onEditPrintTemplateClick,
  className,
  searchParams
}) => {
  return (
    <AritoActionBar
      className={className}
      titleComponent={
        <div>
          <h1 className='text-xl font-bold'>Báo cáo tình trạng đơn hàng</h1>
          <div className='text-[10px] text-gray-500'>
            <p>
              <span className='mr-2 inline-block h-2 w-2 rounded-full bg-red-600'></span>
              <span className='font-semibold'>
                {searchParams?.ngay_tu ? `Từ ngày ${searchParams.ngay_tu}` : 'Từ ngày --/--/----'}
              </span>
              <span> đến ngày </span>
              <span className='font-semibold'>{searchParams?.ngay_den ? searchParams.ngay_den : '--/--/----'}</span>
              {searchParams?.trang_thai && (
                <>
                  <span className='ml-1'>,Trạng thái: </span>
                  <span className='font-semibold'>
                    {searchParams.trang_thai === 'all' && 'Tất cả'}
                    {searchParams.trang_thai === 'draft' && 'Lập chứng từ'}
                    {searchParams.trang_thai === 'waitingForApproval' && 'Chờ duyệt'}
                    {searchParams.trang_thai === 'approving' && 'Đang duyệt'}
                    {searchParams.trang_thai === 'approved' && 'Đã duyệt'}
                    {searchParams.trang_thai === 'processing' && 'Đang thực hiện'}
                    {searchParams.trang_thai === 'completed' && 'Hoàn thành'}
                    {searchParams.trang_thai === 'closed' && 'Đóng'}
                  </span>
                </>
              )}
            </p>
          </div>
        </div>
      }
    >
      {onSearchClick && <AritoActionButton title='Tìm kiếm' icon={Search} onClick={onSearchClick} variant='primary' />}
      {/* No print button */}
      {onRefreshClick && (
        <AritoActionButton title='Refresh' icon={RefreshCw} onClick={onRefreshClick} variant='destructive' />
      )}
      {onFixedColumnsClick && (
        <AritoActionButton title='Cố định cột' icon={Pin} onClick={onFixedColumnsClick} variant='secondary' />
      )}
      {onExportDataClick && (
        <AritoActionButton title='Kết xuất dữ liệu' icon={Sheet} onClick={onExportDataClick} variant='secondary' />
      )}
      <AritoMenuButton
        items={[
          {
            title: 'Chỉnh sửa mẫu in',
            icon: <AritoIcon icon={864} />,
            onClick: onEditPrintTemplateClick,
            group: 0
          }
        ]}
      />
    </AritoActionBar>
  );
};

export default ActionBar;
