import React from 'react';
import { AritoDialog } from '@/components/custom/arito';
import { Button } from '@/components/ui/button';
import { Group } from '@/types/schemas';

interface ConfirmDialogProps {
  open: boolean;
  selectedObj: Group | null;
  onClose: () => void;
  deleteGroup: (uuid: string) => Promise<boolean | undefined | any>;
  clearSelection: () => void;
}

export const ConfirmDialog: React.FC<ConfirmDialogProps> = ({
  open,
  selectedObj,
  onClose,
  deleteGroup,
  clearSelection
}) => {
  const handleDelete = async () => {
    if (selectedObj) {
      try {
        await deleteGroup(selectedObj.uuid);
        onClose();
        clearSelection();
      } catch (error) {
        console.error('Error deleting group:', error);
      }
    }
  };

  return (
    <AritoDialog
      open={open}
      onClose={onClose}
      title='Xác nhận xóa'
      maxWidth='sm'
      disableBackdropClose={false}
      disableEscapeKeyDown={false}
    >
      <div className='p-4'>
        <p className='mb-4'>
          <PERSON>ạn có chắc chắn muốn xóa nhóm kho hàng <strong>{selectedObj?.ten_phan_nhom}</strong>?
        </p>
        <div className='flex justify-end gap-2'>
          <Button variant='outline' onClick={onClose}>
            Hủy
          </Button>
          <Button variant='destructive' onClick={handleDelete}>
            Xóa
          </Button>
        </div>
      </div>
    </AritoDialog>
  );
};
