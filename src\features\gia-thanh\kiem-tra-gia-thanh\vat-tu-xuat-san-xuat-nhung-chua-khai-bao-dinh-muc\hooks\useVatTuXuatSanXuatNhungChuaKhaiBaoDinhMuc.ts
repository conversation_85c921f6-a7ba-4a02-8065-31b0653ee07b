import { useState, useCallback } from 'react';
import {
  VatTuXuatSanXuatNhungChuaKhaiBaoDinhMucItem,
  VatTuXuatSanXuatNhungChuaKhaiBaoDinhMucResponse,
  SearchFormValues,
  UseVatTuXuatSanXuatNhungChuaKhaiBaoDinhMucReturn
} from '../types';
import api from '@/lib/api';

const generateMockData = (): VatTuXuatSanXuatNhungChuaKhaiBaoDinhMucItem[] => {
  return [
    {
      id: '1',
      ma_chung_tu: 'PX001',
      ten_chung_tu: 'Phiếu xuất kho nguyên liệu',
      ngay_chung_tu: '2024-01-15',
      so_chung_tu: 'PX2024001',
      ma_vat_tu: 'VT001',
      ten_vat_tu: 'Thép tấm A36 dày 10mm',
      dien_giai: '<PERSON><PERSON>t thép tấm cho sản xuất khung máy'
    },
    {
      id: '2',
      ma_chung_tu: 'PX002',
      ten_chung_tu: 'Phiếu xuất vật tư phụ',
      ngay_chung_tu: '2024-01-16',
      so_chung_tu: 'PX2024002',
      ma_vat_tu: 'VT002',
      ten_vat_tu: 'Ốc vít M8x20',
      dien_giai: 'Xuất ốc vít cho lắp ráp sản phẩm'
    },
    {
      id: '3',
      ma_chung_tu: 'PX003',
      ten_chung_tu: 'Phiếu xuất nguyên liệu',
      ngay_chung_tu: '2024-01-17',
      so_chung_tu: 'PX2024003',
      ma_vat_tu: 'VT003',
      ten_vat_tu: 'Dây điện đồng 2.5mm²',
      dien_giai: 'Xuất dây điện cho hệ thống điện máy'
    },
    {
      id: '4',
      ma_chung_tu: 'PX004',
      ten_chung_tu: 'Phiếu xuất vật liệu',
      ngay_chung_tu: '2024-01-18',
      so_chung_tu: 'PX2024004',
      ma_vat_tu: 'VT004',
      ten_vat_tu: 'Sơn chống gỉ màu xám',
      dien_giai: 'Xuất sơn cho công đoạn sơn phủ'
    },
    {
      id: '5',
      ma_chung_tu: 'PX005',
      ten_chung_tu: 'Phiếu xuất linh kiện',
      ngay_chung_tu: '2024-01-19',
      so_chung_tu: 'PX2024005',
      ma_vat_tu: 'VT005',
      ten_vat_tu: 'Bearing 6205-2RS',
      dien_giai: 'Xuất bearing cho lắp ráp động cơ'
    },
    {
      id: '6',
      ma_chung_tu: 'PX006',
      ten_chung_tu: 'Phiếu xuất nguyên liệu',
      ngay_chung_tu: '2024-01-20',
      so_chung_tu: 'PX2024006',
      ma_vat_tu: 'VT006',
      ten_vat_tu: 'Nhựa PVC ống 90mm',
      dien_giai: 'Xuất ống nhựa cho hệ thống dẫn khí'
    },
    {
      id: '7',
      ma_chung_tu: 'PX007',
      ten_chung_tu: 'Phiếu xuất phụ kiện',
      ngay_chung_tu: '2024-01-21',
      so_chung_tu: 'PX2024007',
      ma_vat_tu: 'VT007',
      ten_vat_tu: 'Gasket cao su NBR',
      dien_giai: 'Xuất gasket cho lắp ráp van điều khiển'
    },
    {
      id: '8',
      ma_chung_tu: 'PX008',
      ten_chung_tu: 'Phiếu xuất vật tư',
      ngay_chung_tu: '2024-01-22',
      so_chung_tu: 'PX2024008',
      ma_vat_tu: 'VT008',
      ten_vat_tu: 'Dầu thủy lực ISO 46',
      dien_giai: 'Xuất dầu thủy lực cho hệ thống nâng hạ'
    },
    {
      id: '9',
      ma_chung_tu: 'PX009',
      ten_chung_tu: 'Phiếu xuất kim loại',
      ngay_chung_tu: '2024-01-23',
      so_chung_tu: 'PX2024009',
      ma_vat_tu: 'VT009',
      ten_vat_tu: 'Nhôm thanh 6061-T6',
      dien_giai: 'Xuất nhôm thanh cho gia công khung'
    },
    {
      id: '10',
      ma_chung_tu: 'PX010',
      ten_chung_tu: 'Phiếu xuất điện tử',
      ngay_chung_tu: '2024-01-24',
      so_chung_tu: 'PX2024010',
      ma_vat_tu: 'VT010',
      ten_vat_tu: 'IC điều khiển PLC',
      dien_giai: 'Xuất IC cho lắp ráp bảng điều khiển'
    }
  ];
};

/**
 * Custom hook for managing VatTuXuatSanXuatNhungChuaKhaiBaoDinhMuc data
 *
 * This hook provides functionality to fetch materials production tracking data
 * with mock support for testing and development purposes.
 */
export function useVatTuXuatSanXuatNhungChuaKhaiBaoDinhMuc(
  searchParams: SearchFormValues
): UseVatTuXuatSanXuatNhungChuaKhaiBaoDinhMucReturn {
  const [data, setData] = useState<VatTuXuatSanXuatNhungChuaKhaiBaoDinhMucItem[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<Error | null>(null);

  const fetchData = useCallback(async (searchParams: SearchFormValues) => {
    setIsLoading(true);
    setError(null);

    try {
      const mockData = generateMockData();

      const response = await api.get<VatTuXuatSanXuatNhungChuaKhaiBaoDinhMucResponse>(
        '/gia-thanh/kiem-tra-gia-thanh/vat-tu-xuat-san-xuat-nhung-chua-khai-bao-dinh-muc/',
        {
          mock: true,
          mockData,
          params: searchParams
        }
      );

      setData(response.data.results);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred while fetching data';
      setError(new Error(errorMessage));
      setData([]);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const refreshData = useCallback(async () => {
    await fetchData(searchParams);
  }, [fetchData, searchParams]);

  return {
    data,
    isLoading,
    error,
    fetchData,
    refreshData
  };
}
