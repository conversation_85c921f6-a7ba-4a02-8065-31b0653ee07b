import { GridColDef } from '@mui/x-data-grid';

// Column definitions for VAT tax declaration
export const vatTaxDeclarationColumns: GridColDef[] = [
  { field: 'stt_in', headerName: 'Stt in', width: 150 },
  { field: 'chi_tieu', headerName: 'Chỉ tiêu', width: 350 },
  { field: 'ma_so', headerName: 'Mã số', width: 150 },
  { field: 'doanh_so', headerName: '<PERSON>anh số', width: 150 },
  { field: 'thue', headerName: 'Thuế', width: 150 }
];

export const vatTaxDeclarationPL43Columns: GridColDef[] = [
  { field: 'stt_in', headerName: 'Stt in', width: 150 },
  { field: 'ten_hhdv', headerName: 'Tên hàng hóa dịch vụ', width: 350 },
  { field: 'ma_so', headerName: 'Mã số', width: 150 },
  { field: 'tien_chua_thue', headerName: 'Tiền chưa thuế', width: 150 },
  { field: 'thue_suat', headerName: 'Thuế suất quy định', width: 150 },
  { field: 'thue_suat_duoc_giam', headerName: 'Thuế suất được giảm', width: 150 },
  { field: 'thue_duoc_giam', headerName: 'Thuế được giảm', width: 150 }
];

export const bangKeBanRaColumns: GridColDef[] = [
  {
    field: 'so_hoa_don',
    headerName: 'Số hóa đơn',
    width: 130,
    sortable: true
  },
  {
    field: 'ngay_hoa_don',
    headerName: 'Ngày hóa đơn',
    width: 130,
    type: 'date'
  },
  {
    field: 'ky_hieu',
    headerName: 'Ký hiệu',
    width: 100,
    sortable: true
  },
  {
    field: 'ten_khach_hang',
    headerName: 'Tên khách hàng',
    width: 200,
    sortable: true
  },
  {
    field: 'ten_mat_hang',
    headerName: 'Tên mặt hàng',
    width: 200,
    sortable: true
  },
  {
    field: 'ma_so_thue',
    headerName: 'Mã số thuế',
    width: 120,
    sortable: true
  },
  {
    field: 'tien_ban',
    headerName: 'Tiền bán',
    width: 130,
    type: 'number',
    align: 'right',
    headerAlign: 'right'
  },
  {
    field: 'thue_suat',
    headerName: 'Thuế suất',
    width: 100,
    type: 'number',
    align: 'right',
    headerAlign: 'right'
  },
  {
    field: 'tien_thue',
    headerName: 'Tiền thuế',
    width: 130,
    type: 'number',
    align: 'right',
    headerAlign: 'right'
  },
  {
    field: 'ghi_chu',
    headerName: 'Ghi chú',
    width: 150,
    sortable: true
  }
];

export const pl142Columns: GridColDef[] = [
  {
    field: 'ten_hang_hoa_dich_vu',
    headerName: 'Tên hàng hóa, dịch vụ',
    width: 250,
    sortable: true
  },
  {
    field: 'ma_so',
    headerName: 'Mã số',
    width: 120,
    sortable: true
  },
  {
    field: 'tien_chua_thue_nt',
    headerName: 'Tiền chưa thuế nt',
    width: 150,
    type: 'number',
    align: 'right',
    headerAlign: 'right'
  },
  {
    field: 'tien_chua_thue',
    headerName: 'Tiền chưa thuế',
    width: 150,
    type: 'number',
    align: 'right',
    headerAlign: 'right'
  },
  {
    field: 'thue_suat_quy_dinh',
    headerName: 'Thuế suất quy định',
    width: 150,
    type: 'number',
    align: 'right',
    headerAlign: 'right'
  },
  {
    field: 'thue_suat_sau_giam',
    headerName: 'Thuế suất sau giảm',
    width: 150,
    type: 'number',
    align: 'right',
    headerAlign: 'right'
  },
  {
    field: 'thue_duoc_giam_nt',
    headerName: 'Thuế được giảm nt',
    width: 150,
    type: 'number',
    align: 'right',
    headerAlign: 'right'
  },
  {
    field: 'thue_duoc_giam',
    headerName: 'Thuế được giảm',
    width: 150,
    type: 'number',
    align: 'right',
    headerAlign: 'right'
  },
  {
    field: 'thue_gtgt_cua_hh_dv_nt',
    headerName: 'Thuế GTGT của HH, DV nt',
    width: 180,
    type: 'number',
    align: 'right',
    headerAlign: 'right'
  },
  {
    field: 'thue_gtgt_cua_hh_dv',
    headerName: 'Thuế GTGT của HH, DV',
    width: 180,
    type: 'number',
    align: 'right',
    headerAlign: 'right'
  }
];

export const bangKeMuaVaoColumns: GridColDef[] = [
  {
    field: 'so_hoa_don',
    headerName: 'Số hóa đơn',
    width: 130,
    sortable: true
  },
  {
    field: 'ngay_hoa_don',
    headerName: 'Ngày hóa đơn',
    width: 130,
    type: 'date'
  },
  {
    field: 'so_chung_tu',
    headerName: 'Số chứng từ',
    width: 130,
    sortable: true
  },
  {
    field: 'ky_hieu',
    headerName: 'Ký hiệu',
    width: 100,
    sortable: true
  },
  {
    field: 'ten_khach_hang',
    headerName: 'Tên khách hàng',
    width: 200,
    sortable: true
  },
  {
    field: 'ma_so_thue',
    headerName: 'Mã số thuế',
    width: 120,
    sortable: true
  },
  {
    field: 'ten_mat_hang',
    headerName: 'Tên mặt hàng',
    width: 200,
    sortable: true
  },
  {
    field: 'tien_chua_thue',
    headerName: 'Tiền chưa thuế',
    width: 130,
    type: 'number',
    align: 'right',
    headerAlign: 'right'
  },
  {
    field: 'thue_suat',
    headerName: 'Thuế suất',
    width: 100,
    type: 'number',
    align: 'right',
    headerAlign: 'right'
  },
  {
    field: 'tien_thue',
    headerName: 'Tiền thuế',
    width: 130,
    type: 'number',
    align: 'right',
    headerAlign: 'right'
  },
  {
    field: 'tong_tien',
    headerName: 'Tổng tiền',
    width: 130,
    type: 'number',
    align: 'right',
    headerAlign: 'right'
  },
  {
    field: 'ghi_chu',
    headerName: 'Ghi chú',
    width: 150,
    sortable: true
  }
];

// Search columns for department lookup
export const quyenChungTuSearchColumns: GridColDef[] = [
  { field: 'ma_quyen', headerName: 'Mã quyển', width: 150 },
  { field: 'ten_quyen', headerName: 'Tên quyển', width: 250 },
  { field: 'so_ct_mau', headerName: 'Số khai báo', width: 250 }
];
