import { FormField } from '@/components/custom/arito/form/form-field';
import { useDonViCoSo } from '@/hooks/queries';
import { Label } from '@/components/ui/label';

const BasicInfo: React.FC = () => {
  const { donViCoSos, isLoading } = useDonViCoSo();

  // options
  const donViOptions = donViCoSos.map(donVi => ({
    value: donVi.uuid,
    label: `${donVi.ma_unit} - ${donVi.ten_unit}`
  }));

  return (
    <div className='max-h-[calc(100vh-150px)] w-[800px] min-w-[800px] overflow-y-auto'>
      <div className='space-y-2 p-4'>
        <div className='flex flex-col space-y-3'>
          <div className='flex items-center'>
            <FormField
              label='Kỳ'
              labelClassName='w-40'
              name='month'
              type='number'
              className='w-64'
            />
          </div>
          <div className='flex items-center'>
            <FormField
              label='Năm'
              labelClassName='w-40'
              name='year'
              type='number'
              className='w-64'
            />
          </div>
          {/* Đơn vị */}
          <div className='flex items-center'>
            <FormField
              label='Đơn vị'
              labelClassName='w-40'
              type='select'
              className='w-96'
              name='don_vi'
              options={donViOptions}
              disabled={isLoading}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default BasicInfo;
