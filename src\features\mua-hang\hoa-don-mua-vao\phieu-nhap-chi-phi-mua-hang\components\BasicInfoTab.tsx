import {
  NhaCungCapSearchColDef,
  TaiKhoanSearchColDef,
  QuyenSoChungTuSearchColDef
} from '@/features/mua-hang/hoa-don-mua-vao/phieu-nhap-chi-phi-mua-hang/cols-definition';
import { PaymentTermSelectField } from '@/components/custom/arito/form/search-fields/PaymentTermSelectField';
import { SearchField } from '@/components/custom/arito/form/custom-search-field';
import { FormField } from '@/components/custom/arito/form/form-field';
import AritoIcon from '@/components/custom/arito/icon';
import { Label } from '@/components/ui/label';

interface Props {
  formMode: 'add' | 'edit' | 'view';
  setShowTaxCodePopupForm: (value: boolean) => void;
  setShowGetInfoPopupForm: (value: boolean) => void;
}

export const BasicInformationTab = ({ formMode, setShowTaxCodePopupForm, setShowGetInfoPopupForm }: Props) => (
  <div className='p-4'>
    <div className='grid gap-x-4 gap-y-4 lg:grid-cols-[2fr,1fr,1fr]'>
      {/* Left column - 3/5 */}
      <div className='lg:col-span-1'>
        <div className='mb-4 flex items-center gap-x-1'>
          <Label className='min-w-[160px]'>Mã nhà cung cấp</Label>
          <div className='flex-1'>
            <SearchField
              name='ma_nha_cung_cap'
              disabled={formMode === 'view'}
              searchEndpoint='accounting/supplier'
              searchColumns={NhaCungCapSearchColDef}
              columnDisplay='ma_doi_tuong'
              displayRelatedField='ten_doi_tuong'
            />
          </div>
        </div>
        <FormField
          label='Tên nhà cung cấp'
          className='flex items-center gap-x-1'
          labelClassName='min-w-[160px]'
          name='ten_nha_cung_cap'
          type='text'
          disabled={formMode === 'view'}
        />
        <FormField
          label='Địa chỉ'
          className='flex items-center gap-x-1'
          labelClassName='min-w-[160px]'
          name='dia_chi'
          type='text'
          disabled={formMode === 'view'}
        />

        <div className='mb-4 flex items-center gap-x-1'>
          <Label className='min-w-[160px]'>Tài khoản có</Label>
          <div className='flex-1'>
            <SearchField
              name='tai_khoan_co'
              disabled={formMode === 'view'}
              searchEndpoint='accounting/account'
              searchColumns={TaiKhoanSearchColDef}
              columnDisplay='ma_tai_khoan'
              displayRelatedField='ten_tai_khoan'
            />
          </div>
        </div>

        <FormField
          label='Diễn giải'
          className='flex items-center gap-x-1'
          labelClassName='min-w-[160px]'
          name='dien_giai'
          type='text'
          disabled={formMode === 'view'}
        />
        <div className='grid grid-cols-1 lg:grid lg:grid-cols-[1fr,1fr]'>
          <FormField
            className='flex items-center gap-x-1'
            labelClassName='min-w-[160px]'
            label='Chứng từ'
            name='chung_tu'
            type='select'
            disabled={formMode === 'view'}
            options={[
              { value: 0, label: 'Hóa đơn mua hàng trong nước' },
              { value: 1, label: 'Hóa đơn mua hàng nhập khẩu' },
              { value: 2, label: 'Hóa đơn mua hàng nhập mua - xuất thẳng' }
            ]}
          />
          <button
            type='button'
            className='flex w-[300px] max-w-40 items-center justify-center gap-x-2 rounded-md border border-gray-300 p-2 text-xs font-semibold hover:bg-gray-100'
            onClick={() => setShowGetInfoPopupForm(true)}
          >
            <AritoIcon icon={276} />
            Lấy dữ liệu
          </button>
        </div>
      </div>

      {/* Middle column - 1/5 */}
      <div className='lg:col-span-1'>
        <div className='flex items-center gap-x-2'>
          <FormField
            type='text'
            className='flex items-center gap-x-1'
            labelClassName='min-w-[160px]'
            label='Mã số thuế'
            name='taxCode'
            disabled={formMode === 'view'}
          />
          <div className='cursor-pointer'>
            <AritoIcon icon={15} />
          </div>
          <div className='cursor-pointer' onClick={() => setShowTaxCodePopupForm(true)}>
            <AritoIcon icon={58} />
          </div>
        </div>

        <FormField
          label='Người giao hàng'
          className='flex items-center gap-x-1'
          labelClassName='min-w-[160px]'
          name='nguoi_giao_hang'
          type='text'
          disabled={formMode === 'view'}
        />

        <FormField
          label='Email'
          className='flex items-center gap-x-1'
          labelClassName='min-w-[160px]'
          name='email'
          type='text'
          disabled={formMode === 'view'}
        />

        <PaymentTermSelectField
          className='flex items-center gap-x-1'
          label='Hạn thanh toán'
          name='han_thanh_toan'
          disabled={formMode === 'view'}
          inputClassName='w-64'
          labelClassName='min-w-[160px]'
        />
      </div>

      {/* Right column - 1/5 */}
      <div className='lg:col-span-1'>
        <div className='mb-4 flex items-center gap-x-1'>
          <Label className='min-w-[120px]'>Số chứng từ</Label>
          <div className='flex-1'>
            <SearchField
              name='so_chung_tu'
              disabled={formMode === 'view'}
              searchEndpoint='accounting/transaction'
              searchColumns={QuyenSoChungTuSearchColDef}
              columnDisplay='ma_quyen'
              displayRelatedField='ten_quyen'
            />
          </div>
        </div>

        <FormField
          label='Ngày chứng từ'
          className='flex items-center gap-x-1'
          labelClassName='min-w-[120px]'
          name='ngay_chung_tu'
          type='date'
          disabled={formMode === 'view'}
        />

        <FormField
          label='Số hoá đơn'
          className='flex items-center gap-x-1'
          labelClassName='min-w-[120px]'
          name='so_hoa_don'
          type='number'
          disabled={formMode === 'view'}
        />
        <FormField
          label='Ký hiệu'
          className='flex items-center gap-x-1'
          labelClassName='min-w-[120px]'
          name='ky_hieu'
          type='text'
          disabled={formMode === 'view'}
        />
        <FormField
          label='Ngày hoá đơn'
          className='flex items-center gap-x-1'
          labelClassName='min-w-[120px]'
          name='ngay_hoa_don'
          type='date'
          disabled={formMode === 'view'}
        />
        <div className='grid grid-cols-[100px,1fr,1fr] gap-x-4'>
          <div className='mt-3 flex min-w-[120px] items-center pr-2 text-left text-sm font-normal text-black peer-disabled:cursor-not-allowed peer-disabled:opacity-70 sm:mb-0'>
            Ngoại tệ
          </div>
          <FormField
            label=''
            className='flex items-center gap-x-1'
            name='ngoai_te'
            type='select'
            disabled={formMode === 'view'}
            options={[
              { value: 0, label: 'VND' },
              { value: 1, label: 'USD' },
              { value: 2, label: 'EUR' },
              { value: 3, label: 'JPY' }
            ]}
          />
          <FormField
            label=''
            className='flex items-center gap-x-1'
            name='ty_gia'
            type='number'
            disabled={formMode === 'view'}
          />
        </div>

        <FormField
          label='Trạng thái'
          className='flex items-center gap-x-1'
          labelClassName='min-w-[120px]'
          name='trang_thai'
          type='select'
          disabled={formMode === 'view'}
          options={[
            { value: 0, label: 'Chưa ghi sổ' },
            { value: 1, label: 'Chờ duyệt' },
            { value: 2, label: 'Đã ghi sổ' }
          ]}
        />

        <div className='flex w-full items-center py-2 pl-[100px]'>
          <FormField
            className='flex items-center gap-x-1'
            labelClassName='min-w-[160px]'
            type='checkbox'
            label='Dữ liệu được nhận'
            name='du_lieu_duoc_nhan'
            disabled={formMode === 'view'}
          />
        </div>
      </div>
    </div>
  </div>
);
