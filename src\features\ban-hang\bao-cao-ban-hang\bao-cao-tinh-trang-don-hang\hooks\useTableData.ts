import React, { useState, useEffect } from 'react';
import { TableData } from '@/components/custom/arito/data-tables/types';

export interface UseTableDataReturn {
  tables: TableData[];
  handleRowClick: (params: any) => void;
}

export function useTableData(): UseTableDataReturn {
  const [tables, setTables] = useState<TableData[]>([
    {
      name: '',
      columns: [
        {
          field: 'unit_id',
          headerName: 'Đơn vị',
          width: 150
        },
        {
          field: 'ma_ngv',
          headerName: 'Loại chứng từ',
          width: 150
        },
        {
          field: 'ngay_ct',
          headerName: 'Ngày c/từ',
          width: 120
        },
        {
          field: 'so_ct',
          headerName: 'Số c/từ',
          width: 120
        },
        {
          field: 'ma_kh',
          headerName: 'Mã khách hàng',
          width: 150
        },
        {
          field: 'ten_kh',
          headerName: 'Tên khách hàng',
          width: 200
        },
        {
          field: 'ma_nvbh',
          headerName: '<PERSON><PERSON> nhân viên bán hàng',
          width: 150
        },
        {
          field: 'ten_nvbh',
          headerName: 'Tên nhân viên bán hàng',
          width: 200
        },
        {
          field: 'status',
          headerName: 'Trạng thái',
          width: 120
        },
        {
          field: 'ma_vt',
          headerName: 'Mã vật tư',
          width: 150
        },
        {
          field: 'ten_vt',
          headerName: 'Tên vật tư',
          width: 200
        },
        {
          field: 'gia2',
          headerName: 'Đơn giá',
          width: 120
        },
        {
          field: 'tien2',
          headerName: 'Thành tiền',
          width: 120
        },
        {
          field: 'sl_dh',
          headerName: 'Sl đặt hàng',
          width: 150
        },
        {
          field: 'so_luong',
          headerName: 'Sl đặt đơn hàng',
          width: 150
        },
        {
          field: 'sl_xuat',
          headerName: 'Sl đã xuất',
          width: 150
        },
        {
          field: 'sl_hd',
          headerName: 'Sl hóa đơn',
          width: 150
        },
        {
          field: 'sl_tl',
          headerName: 'Sl trả lại',
          width: 150
        },
        {
          field: 'sl_cl',
          headerName: 'Sl còn lại',
          width: 150
        },
        {
          field: 'ma_bp',
          headerName: 'Bộ phận',
          width: 150
        },
        {
          field: 'ma_vv',
          headerName: 'Vụ việc',
          width: 150
        },
        {
          field: 'ma_hd',
          headerName: 'Hợp đồng',
          width: 150
        },
        {
          field: 'ma_dtt',
          headerName: 'Đợt thanh toán',
          width: 150
        },
        {
          field: 'ma_ku',
          headerName: 'Khế ước',
          width: 150
        },
        {
          field: 'ma_phi',
          headerName: 'Phí',
          width: 150
        },
        {
          field: 'ma_sp',
          headerName: 'Sản phẩm',
          width: 150
        },
        {
          field: 'ma_lsx',
          headerName: 'Lệnh sản xuất',
          width: 150
        },
        {
          field: 'ma_cp0',
          headerName: 'C/p không h/lệ',
          width: 150
        }
      ],
      rows: []
    }
  ]);

  // Initialize with sample data
  React.useEffect(() => {
    setTables(prevTables => {
      const updatedTables = [...prevTables];
      updatedTables[0].rows = [
        {
          customerCode: '',
          customerName: 'Tổng cộng',
          debitAmount: 5000000,
          creditAmount: 2000000
        },
        {
          customerCode: 'KH002',
          customerName: 'Công ty Cổ phần XYZ',
          debitAmount: 10000000,
          creditAmount: 8000000
        },
        {
          customerCode: 'KH003',
          customerName: 'Doanh nghiệp tư nhân Bình Minh',
          debitAmount: 3000000,
          creditAmount: 1000000
        },
        {
          customerCode: 'KH004',
          customerName: 'Công ty TNHH Minh Long',
          debitAmount: 0,
          creditAmount: 500000
        },
        {
          customerCode: 'KH005',
          customerName: 'Công ty TNHH Thịnh Vượng',
          debitAmount: 7500000,
          creditAmount: 7500000
        }
      ];
      return updatedTables;
    });
  }, []);

  const handleRowClick = (params: any) => {
    console.log('Row clicked:', params);
  };

  return {
    tables,

    handleRowClick
  };
}
