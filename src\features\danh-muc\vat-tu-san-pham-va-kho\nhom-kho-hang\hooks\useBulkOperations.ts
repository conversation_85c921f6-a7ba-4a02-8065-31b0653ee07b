import { useState, useRef, useEffect } from 'react';
import { Group, GroupType } from '@/types/schemas';
import { useGroup } from '@/hooks/queries';

export const useBulkOperations = (activeGroup: GroupType) => {
  const [selectedItems, setSelectedItems] = useState<string[]>([]);
  const [isProcessing, setIsProcessing] = useState<boolean>(false);
  const selectedItemsRef = useRef<string[]>([]);

  const { deleteGroup } = useGroup(activeGroup);
  const deleteGroupRef = useRef(deleteGroup);

  useEffect(() => {
    selectedItemsRef.current = selectedItems;
    deleteGroupRef.current = deleteGroup;
  }, [selectedItems, deleteGroup]);

  const handleSelectionChange = (newSelection: string[]) => {
    setSelectedItems(newSelection);
  };

  const clearSelection = () => {
    setSelectedItems([]);
  };

  const bulkDelete = async (refreshGroups: () => Promise<void>) => {
    if (selectedItemsRef.current.length === 0) return;

    setIsProcessing(true);

    try {
      const deletePromises = selectedItemsRef.current.map(uuid => deleteGroupRef.current(uuid));
      await Promise.all(deletePromises);

      await refreshGroups();

      clearSelection();

      return true;
    } catch (error) {
      console.error('Error deleting groups:', error);
      throw error;
    } finally {
      setIsProcessing(false);
    }
  };

  const bulkUpdateStatus = async (
    status: string,
    groups: Group[],
    updateGroup: (uuid: string, data: any) => Promise<any>,
    refreshGroups: () => Promise<void>
  ) => {
    if (selectedItemsRef.current.length === 0) return;

    setIsProcessing(true);

    try {
      const selectedGroups = groups.filter(group => selectedItemsRef.current.includes(group.uuid));

      const updatePromises = selectedGroups.map(group =>
        updateGroup(group.uuid, {
          ...group,
          trang_thai: status,
          loai_nhom: activeGroup
        })
      );

      await Promise.all(updatePromises);

      await refreshGroups();

      clearSelection();

      return true;
    } catch (error) {
      console.error('Error updating groups:', error);
      throw error;
    } finally {
      setIsProcessing(false);
    }
  };

  return {
    selectedItems,
    isProcessing,
    handleSelectionChange,
    clearSelection,
    bulkDelete,
    bulkUpdateStatus
  };
};
