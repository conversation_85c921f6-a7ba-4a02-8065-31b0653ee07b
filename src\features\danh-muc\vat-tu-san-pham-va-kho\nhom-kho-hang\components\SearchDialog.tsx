import { Search, X } from 'lucide-react';
import React, { useState } from 'react';
import { AritoDialog } from '@/components/custom/arito';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

interface SearchDialogProps {
  open: boolean;
  onClose: () => void;
  onSearch: (searchTerm: string) => void;
  initialSearchTerm?: string;
}

export const SearchDialog: React.FC<SearchDialogProps> = ({ open, onClose, onSearch, initialSearchTerm = '' }) => {
  const [searchTerm, setSearchTerm] = useState<string>(initialSearchTerm);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSearch(searchTerm);
    onClose();
  };

  const handleClear = () => {
    setSearchTerm('');
  };

  return (
    <AritoDialog
      open={open}
      onClose={onClose}
      title='Tìm kiếm nhóm kho hàng'
      maxWidth='sm'
      disableBackdropClose={false}
      disableEscapeKeyDown={false}
    >
      <form onSubmit={handleSubmit} className='p-4'>
        <div className='mb-4'>
          <Label htmlFor='searchTerm' className='mb-2 block'>
            Nhập từ khóa tìm kiếm
          </Label>
          <div className='relative'>
            <Input
              id='searchTerm'
              value={searchTerm}
              onChange={e => setSearchTerm(e.target.value)}
              placeholder='Tìm theo mã nhóm, tên nhóm...'
              className='pr-10'
              autoFocus
            />
            {searchTerm && (
              <button
                type='button'
                onClick={handleClear}
                className='absolute right-2 top-1/2 -translate-y-1/2 text-gray-500 hover:text-gray-700'
              >
                <X className='h-4 w-4' />
              </button>
            )}
          </div>
          <p className='mt-1 text-xs text-gray-500'>Tìm kiếm theo mã nhóm, tên nhóm hoặc tên 2</p>
        </div>

        <div className='flex justify-end gap-2'>
          <Button type='button' variant='outline' onClick={onClose}>
            Hủy
          </Button>
          <Button type='submit' className='flex items-center gap-1'>
            <Search className='h-4 w-4' />
            Tìm kiếm
          </Button>
        </div>
      </form>
    </AritoDialog>
  );
};
