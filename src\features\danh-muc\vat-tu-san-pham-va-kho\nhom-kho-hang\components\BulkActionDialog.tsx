import React from 'react';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { AritoDialog } from '@/components/custom/arito';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Group } from '@/types/schemas';

interface BulkActionDialogProps {
  open: boolean;
  onClose: () => void;
  selectedItems: string[];
  groups: Group[];
  onBulkDelete: () => Promise<void>;
  onBulkUpdateStatus: (status: string) => Promise<void>;
}

export const BulkActionDialog: React.FC<BulkActionDialogProps> = ({
  open,
  onClose,
  selectedItems,
  groups,
  onBulkDelete,
  onBulkUpdateStatus
}) => {
  const [action, setAction] = React.useState<'delete' | 'status'>('status');
  const [status, setStatus] = React.useState<string>('1');
  const [isProcessing, setIsProcessing] = React.useState<boolean>(false);

  const selectedGroups = groups.filter(group => selectedItems.includes(group.uuid));

  const handleSubmit = async () => {
    setIsProcessing(true);

    try {
      if (action === 'delete') {
        await onBulkDelete();
      } else if (action === 'status') {
        await onBulkUpdateStatus(status);
      }

      onClose();
    } catch (error) {
      console.error('Error performing bulk action:', error);
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <AritoDialog
      open={open}
      onClose={onClose}
      title='Thao tác hàng loạt'
      maxWidth='sm'
      disableBackdropClose={false}
      disableEscapeKeyDown={false}
    >
      <div className='p-4'>
        <div className='mb-4'>
          <p className='mb-2 text-sm'>
            Bạn đã chọn <strong>{selectedItems.length}</strong> nhóm kho hàng. Chọn thao tác bạn muốn thực hiện:
          </p>

          <RadioGroup value={action} onValueChange={value => setAction(value as 'delete' | 'status')} className='mt-4'>
            <div className='flex items-center space-x-2'>
              <RadioGroupItem value='status' id='action-status' />
              <Label htmlFor='action-status'>Cập nhật trạng thái</Label>
            </div>
            <div className='flex items-center space-x-2'>
              <RadioGroupItem value='delete' id='action-delete' />
              <Label htmlFor='action-delete'>Xóa các nhóm đã chọn</Label>
            </div>
          </RadioGroup>

          {action === 'status' && (
            <div className='mt-4'>
              <p className='mb-2 text-sm'>Chọn trạng thái mới:</p>
              <RadioGroup value={status} onValueChange={setStatus} className='mt-2'>
                <div className='flex items-center space-x-2'>
                  <RadioGroupItem value='1' id='status-active' />
                  <Label htmlFor='status-active'>Còn sử dụng</Label>
                </div>
                <div className='flex items-center space-x-2'>
                  <RadioGroupItem value='0' id='status-inactive' />
                  <Label htmlFor='status-inactive'>Không sử dụng</Label>
                </div>
              </RadioGroup>
            </div>
          )}

          {action === 'delete' && (
            <div className='mt-4 rounded-md bg-red-50 p-3 text-sm text-red-600'>
              <p>
                <strong>Cảnh báo:</strong> Thao tác này sẽ xóa vĩnh viễn các nhóm kho hàng đã chọn và không thể hoàn
                tác.
              </p>
            </div>
          )}
        </div>

        <div className='flex justify-end gap-2'>
          <Button type='button' variant='outline' onClick={onClose} disabled={isProcessing}>
            Hủy
          </Button>
          <Button
            type='button'
            onClick={handleSubmit}
            disabled={isProcessing}
            variant={action === 'delete' ? 'destructive' : 'default'}
          >
            {isProcessing ? 'Đang xử lý...' : action === 'delete' ? 'Xóa' : 'Cập nhật'}
          </Button>
        </div>
      </div>
    </AritoDialog>
  );
};
